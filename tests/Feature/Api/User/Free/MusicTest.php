<?php

use App\Enums\ModuleType;
use App\Models\Module;
use App\Models\Music;


beforeEach(function () {
    $this->auth_user = actingAsUser();
});

it('get music list', function () {
    $response = $this->getJson(route('get_music', [
        'module_id' => Module::where('type', ModuleType::MUSIC->value)->first()->id
    ]));

    $response->assertSuccessful()
        ->assertJsonPath('success', true);
});

it('get music details', function () {
    $music = Music::factory()->create([
        'module_id' => Module::where('type', ModuleType::MUSIC->value)->first()->id
    ]);
    $response = $this->getJson(route('get_music_detail', [
        'module_id' => Module::where('type', ModuleType::MUSIC->value)->first()->id,
        'music_id' => $music->id
    ]));

    $response->assertSuccessful()
        ->assertJsonPath('id', $music->id);
});
