<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('empower_her_posts', function (Blueprint $table) {
            $table->id();

            $table->foreignId('module_id')
                ->references('id')
                ->on('modules')
                ->onDelete('cascade');

            $table->foreignId('empower_her_type_id')
                ->references('id')
                ->on('empower_her_types')
                ->onDelete('cascade');

            $table->string('title');
            $table->text('content');
            $table->string('thumbnail')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('empower_her_posts');
    }
};
