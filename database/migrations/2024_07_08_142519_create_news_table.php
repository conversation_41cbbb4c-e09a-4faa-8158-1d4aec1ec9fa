<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('news', function (Blueprint $table) {
            $table->id();

            $table->bigInteger('external_id')->unique()->unsigned();
            $table->foreignId('news_source_id')
                ->references('id')
                ->on('news_sources')
                ->onDelete('cascade');
            $table->foreignId('module_id')
                ->references('id')
                ->on('modules')
                ->onDelete('cascade');
            $table->string('title');
            $table->text('description');
            $table->string('url');
            $table->string('author')->nullable();
            $table->string('thumbnail');

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('news');
    }
};
