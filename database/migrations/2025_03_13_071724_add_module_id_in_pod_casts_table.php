<?php

use App\Models\Module;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('pod_casts', function (Blueprint $table) {
            $table->foreignId('module_id')->after('tag_id')
                ->references('id')
                ->on('modules')
                ->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('pod_casts', function (Blueprint $table) {
            $table->dropForeign('pod_casts_module_id_foreign');
            $table->dropColumn('module_id');
        });
    }
};
