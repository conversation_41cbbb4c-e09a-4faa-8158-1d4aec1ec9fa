<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('temp_auth_keys', function (Blueprint $table) {
            $table->timestamp('verified_at')->nullable()->after('key');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('temp_auth_keys', function (Blueprint $table) {
            $table->dropColumn('verified_at');
        });
    }
};
