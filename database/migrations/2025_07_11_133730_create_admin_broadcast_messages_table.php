<?php

use App\Enums\BroadcastMessageType;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('admin_broadcast_messages', function (Blueprint $table) {
            $table->id();
            $table->enum('type', array_column(BroadcastMessageType::cases(), 'value'))->default(BroadcastMessageType::USER->value);
            $table->string('type_value');
            $table->string('title');
            $table->string('body');
            $table->string('url')->nullable();
            $table->foreignId('admin_id')->references('id')->on('admins')->onDelete('cascade');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('admin_broadcast_messages');
    }
};
