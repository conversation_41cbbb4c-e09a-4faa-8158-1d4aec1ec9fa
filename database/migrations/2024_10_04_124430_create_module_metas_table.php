<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('module_metas', function (Blueprint $table) {
            $table->id();
            $table->foreignId('module_id')
                ->references('id')
                ->on('modules')
                ->onDelete('cascade');
            $table->string('type');
            $table->string('title');
            $table->text('description')->nullable();
            $table->text('powered_by_text')->nullable();
            $table->text('powered_by_link')->nullable();
            $table->text('powered_by_logo')->nullable();
            $table->text('akina_logo')->nullable();
            $table->string('bg_image')->nullable();


            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('module_metas');
    }
};
