<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('news_sources', function (Blueprint $table) {
            $table->id();

            $table->bigInteger('external_id')->unique()->unsigned();
            $table->foreignId('module_id')
                ->references('id')
                ->on('modules')
                ->onDelete('cascade');
            $table->string('name');
            $table->string('url');
            $table->string('base_href');
            $table->string('thumbnail');

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('news_sources');
    }
};
