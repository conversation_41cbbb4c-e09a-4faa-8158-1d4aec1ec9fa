<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_notification_preferences', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')
                ->references('id')
                ->on('users')
                ->onDelete('cascade');
            $table->boolean('general_information')->default(false);
            $table->boolean('sound')->default(false);
            $table->boolean('vibrate')->default(false);
            $table->boolean('app_updates')->default(false);
            $table->boolean('bill_reminder')->default(false);
            $table->boolean('promotion')->default(false);
            $table->boolean('discount_available')->default(false);
            $table->boolean('payment_request')->default(false);
            $table->boolean('new_service_available')->default(false);
            $table->boolean('new_tips_available')->default(false);

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_notification_preferences');
    }
};
