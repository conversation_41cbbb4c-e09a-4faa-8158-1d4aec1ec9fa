<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('stripe_payment_logs', function (Blueprint $table) {
            $table->dropColumn('stripe_object_id');

            $table->string('payment_intent_id', 60)->after('action_type')->nullable();
            $table->string('subscription_id', 60)->after('action_type')->nullable();
            $table->string('customer_id', 30)->after('action_type')->nullable();

        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('stripe_payment_logs', function (Blueprint $table) {
            $table->string('stripe_object_id')->nullable();

            $table->dropColumn('payment_intent_id');
            $table->dropColumn('subscription_id');
            $table->dropColumn('customer_id');
        });
    }
};
