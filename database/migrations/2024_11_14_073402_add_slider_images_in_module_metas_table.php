<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('module_metas', function (Blueprint $table) {
            $table->json('slider_images')->after('bg_image')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('module_metas', function (Blueprint $table) {
            $table->dropColumn('slider_images');
        });
    }
};
