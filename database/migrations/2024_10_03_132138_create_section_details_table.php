<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('page_section_details', function (Blueprint $table) {
            $table->id();

            $table->foreignId('page_section_id')
                ->references('id')
                ->on('page_sections')
                ->onDelete('cascade');
            $table->string('title')->nullable();
            $table->string('sub_title')->nullable();
            $table->text('description')->nullable();
            $table->string('asset_type')->nullable();
            $table->string('asset_link')->nullable();
            $table->string('anchor_text')->nullable();
            $table->string('anchor_link')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('page_section_details');
    }
};
