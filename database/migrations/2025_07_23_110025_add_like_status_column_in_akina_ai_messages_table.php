<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('akina_ai_messages', function (Blueprint $table) {
            $table->smallInteger('like_status')
                ->default(0)
                ->comment('0: Neutral, 1: Liked, 2: Disliked')
                ->after('content');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('akina_ai_messages', function (Blueprint $table) {
            $table->dropColumn('like_status');
        });
    }
};
