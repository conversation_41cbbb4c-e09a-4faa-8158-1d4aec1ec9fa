<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('empower_her_posts', function (Blueprint $table) {
            $table->string('button_link')->after('thumbnail')->nullable();
            $table->string('button_text')->after('thumbnail')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('empower_her_posts', function (Blueprint $table) {
            $table->dropColumn('button_link');
            $table->dropColumn('button_text');
        });
    }
};
