<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('user_profiles', function (Blueprint $table) {
            $table->text('education_level')->nullable()->change();
            $table->text('income_level')->nullable()->change();
            $table->text('marital_status')->nullable()->change();
            $table->text('children_option')->nullable()->change();
            $table->text('interests_option')->nullable()->change();
            $table->text('joining_motivation_options')->nullable()->change();
            $table->text('date_of_birth')->nullable()->change();
            $table->text('address')->nullable()->change();
            $table->text('nick_name')->nullable()->change();
            $table->text('phone_number')->nullable()->change();
            $table->text('bio')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
