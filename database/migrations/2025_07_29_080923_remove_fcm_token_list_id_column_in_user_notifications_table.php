<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('user_notifications', function (Blueprint $table) {

            $table->dropForeign('user_notifications_fcm_token_list_id_foreign');
            $table->dropColumn('fcm_token_list_id');

            $table->unsignedBigInteger('admin_broadcast_message_id')
                ->nullable();
            $table->foreign('admin_broadcast_message_id')
                ->references('id')
                ->on('admin_broadcast_messages')
                ->onDelete('SET NULL');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('user_notifications', function (Blueprint $table) {
            $table->unsignedBigInteger('fcm_token_list_id')->nullable();
            $table->foreign('fcm_token_list_id')->references('id')
                ->on('fcm_token_lists')
                ->onDelete('SET NULL');

            $table->dropForeign(['admin_broadcast_message_id']);
            $table->dropColumn('admin_broadcast_message_id');
        });
    }
};
