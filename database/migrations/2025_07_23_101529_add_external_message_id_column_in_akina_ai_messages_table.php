<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('akina_ai_messages', function (Blueprint $table) {
            $table->char('external_message_id', 70)->unique()->nullable()->after('id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('akina_ai_messages', function (Blueprint $table) {
            $table->dropColumn('external_message_id');
        });
    }
};
