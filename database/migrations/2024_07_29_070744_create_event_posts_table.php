<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('events', function (Blueprint $table) {
            $table->id();

            $table->foreignId('module_id')
                ->references('id')
                ->on('modules')
                ->onDelete('cascade');
            $table->foreignId('location_id')
                ->references('id')
                ->on('locations')
                ->onDelete('cascade');

            $table->string('title');
            $table->string('sub_title');
            $table->text('content');
            $table->string('thumbnail');
            $table->string('button_link')->nullable();
            $table->string('button_text')->nullable();
            $table->string('event_date');
            $table->boolean('is_akina_event')->default(false);

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('events');
    }
};
