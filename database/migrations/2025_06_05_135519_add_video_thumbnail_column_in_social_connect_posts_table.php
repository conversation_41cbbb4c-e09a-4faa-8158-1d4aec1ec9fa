<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('social_connect_posts', function (Blueprint $table) {
            $table->string('video_thumbnail')->after('media_link')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('social_connect_posts', function (Blueprint $table) {
            $table->dropColumn('video_thumbnail');
        });
    }
};
