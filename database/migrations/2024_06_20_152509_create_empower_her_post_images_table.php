<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('empower_her_post_images', function (Blueprint $table) {
            $table->id();

            $table->foreignId('empower_her_post_id')
                ->references('id')
                ->on('empower_her_posts')
                ->onDelete('cascade');
            $table->string('image_url');

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('empower_her_post_images');
    }
};
