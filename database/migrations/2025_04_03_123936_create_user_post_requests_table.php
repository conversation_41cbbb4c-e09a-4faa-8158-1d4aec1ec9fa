<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_post_requests', function (Blueprint $table) {
            $table->id();

            $table->foreignId('user_id')
                ->references('id')
                ->on('users')
                ->onDelete('cascade');
            $table->foreignId('post_id')
                ->references('id')
                ->on('posts')
                ->onDelete('cascade');

            $table->tinyInteger('status')->default(0)->comment('0=Pending, 1=Accepted, 2=Rejected');
            $table->string('reason')->nullable()->comment('Reason for rejection/acceptance');

            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_post_requests');
    }
};
