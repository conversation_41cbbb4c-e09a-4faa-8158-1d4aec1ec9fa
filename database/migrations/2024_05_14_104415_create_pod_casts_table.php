<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('pod_casts', function (Blueprint $table) {
            $table->id();

            $table->foreignId('category_id')
                ->nullable()
                ->references('id')
                ->on('categories')
                ->onDelete('cascade');

            $table->foreignId('tag_id')
                ->nullable()
                ->references('id')
                ->on('tags')
                ->onDelete('cascade');

            $table->string('title');
            $table->text('audio_link');
            $table->string('thumbnail'  );

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('pod_casts');
    }
};
