<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('affiliate_codes', function (Blueprint $table) {
            $table->unsignedBigInteger('redeemed_by')->nullable()->after('is_active');
            $table->foreign('redeemed_by')
                ->references('id')
                ->on('users')
                ->onDelete('set null')
                ->onUpdate('cascade');

            $table->timestamp('redeemed_at')->nullable()->after('redeemed_by')
                ->comment('Timestamp when the affiliate code was redeemed');

        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('affiliate_codes', function (Blueprint $table) {
            $table->dropForeign(['redeemed_by']);
            $table->dropColumn(['redeemed_by', 'redeemed_at']);
        });
    }
};
