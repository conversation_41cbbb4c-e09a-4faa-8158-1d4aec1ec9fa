<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('social_connect_reported_posts', function (Blueprint $table) {
            $table->id();

            $table->foreignId('social_connect_post_id')
                ->references('id')
                ->on('social_connect_posts')
                ->onDelete('cascade');
            $table->foreignId('reported_by')
                ->references('id')
                ->on('users')
                ->onDelete('cascade');
            $table->json('report_reason_ids')->nullable();
            $table->tinyInteger('is_blocked_for_all')->default(0)->comment('0=Pending, 1=Yes, 2=No');

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('social_connect_reported_posts');
    }
};
