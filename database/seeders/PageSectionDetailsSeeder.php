<?php

namespace Database\Seeders;

use App\Models\PageSectionDetail;
use Carbon\Carbon;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class PageSectionDetailsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        PageSectionDetail::truncate();
        $now = Carbon::now();

        PageSectionDetail::insert([
            [
                'id' => 1,
                'page_section_id' => 1,
                'title' => 'Header Video',
                'sub_title' => null,
                'description' => null,
                'asset_type' => 'video',
                'asset_link' => 'https://akina-app-v2.s3.amazonaws.com/pages/assets/1728043529_header-video-asset.mp4',
                'anchor_text' => null,
                'anchor_link' => null,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'id' => 2,
                'page_section_id' => 2,
                'title' => 'Access is Empowerment',
                'sub_title' => null,
                'description' => '<p>Akina is revolutionizing how Black women are connected and resourced through technology and community. We are building a first-in-class member media technology company.</p><p>Our platform aims to break down barriers and provide opportunities for Black women to thrive in all aspects of their lives. Our goal is to create a space where Black women can connect with each other, access valuable resources, and feel empowered to pursue their passions and goals.</p><p>We believe that access is empowerment.</p>',
                'asset_type' => 'none',
                'asset_link' => null,
                'anchor_text' => null,
                'anchor_link' => null,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'id' => 4,
                'page_section_id' => 3,
                'title' => 'Ask Akina AI',
                'sub_title' => null,
                'description' => 'In today’s interconnected world, a culturally competent AI tool has become imperative for fostering inclusivity and equitable access to technology. Ask Akina recognizes the diverse cultural backgrounds, beliefs, and values of Black women by understanding the nuances of language, customs, and social norms. Have a question? Ask Akina- your culturally competent AI assistant right at your fingertips!',
                'asset_type' => 'image',
                'asset_link' => 'https://akina-app-v2.s3.amazonaws.com/pages/assets/1728044993_ask-akina-ai-asset.png',
                'anchor_text' => 'Join Akina Connect',
                'anchor_link' => null,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'id' => 5,
                'page_section_id' => 3,
                'title' => 'Networking',
                'sub_title' => null,
                'description' => 'Expand your professional and social network, foster new relationships, and connect with colleagues and mentors across industries. Join in-person and virtual events or connect via the Akina App.',
                'asset_type' => 'image',
                'asset_link' => 'https://akina-app-v2.s3.amazonaws.com/pages/assets/1728045025_networking-asset.jpeg',
                'anchor_text' => 'Join Akina Connect',
                'anchor_link' => null,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'id' => 6,
                'page_section_id' => 3,
                'title' => 'Empowerment Hub',
                'sub_title' => null,
                'description' => 'Discover an empowering hub designed for Black women to thrive no matter where you are in your journey. At Akina, you can find or create a supportive community who understands and uplifts you.',
                'asset_type' => 'image',
                'asset_link' => 'https://akina-app-v2.s3.amazonaws.com/pages/assets/1728045067_empowerment-hub-asset.jpeg',
                'anchor_text' => 'Join Akina Connect',
                'anchor_link' => null,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'id' => 7,
                'page_section_id' => 3,
                'title' => 'Events and meetups',
                'sub_title' => null,
                'description' => 'Attend regular in-person and/or virtual events designed to empower and encourage your journey. Events will include social and professional networking opportunities, seminars, workshops, and Masterclasses taught by experts across industries.',
                'asset_type' => 'image',
                'asset_link' => 'https://akina-app-v2.s3.amazonaws.com/pages/assets/1728045100_events-and-meetups-asset.jpeg',
                'anchor_text' => 'Join Akina Connect',
                'anchor_link' => null,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'id' => 8,
                'page_section_id' => 3,
                'title' => 'Culture Club Perks',
                'sub_title' => null,
                'description' => 'Culture Club Perks is an exclusive initiative for Black women to discover unique savings on a curated range of goods and services from local to national partners who contribute to an inclusive economy dedicated to diversity and excellence.',
                'asset_type' => 'image',
                'asset_link' => 'https://akina-app-v2.s3.amazonaws.com/pages/assets/1728045133_culture-club-perks-asset.jpeg',
                'anchor_text' => 'Join Akina Connect',
                'anchor_link' => null,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'id' => 9,
                'page_section_id' => 4,
                'title' => 'Euphemia Gans, Business Banker, JP Morgan Chase',
                'sub_title' => null,
                'description' => 'Meet Akina is a One-Stop shop for all things relating to women of color. Being a member of the sandwich generation where you’re caring for aging relatives yet trying to rear young adult children, one needs resources and Meet Akina were there. Talking to folks about which industries were hiring young talent was there. Lastly, needing to know where local community events were happening to get involved were there. Meet Akina, keeps delivering in every aspect of an active woman’s life!',
                'asset_type' => 'image',
                'asset_link' => 'https://akina-app-v2.s3.amazonaws.com/pages/assets/1728045260_euphemia-gans-business-banker-jp-morgan-chase-asset.jpeg',
                'anchor_text' => null,
                'anchor_link' => null,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'id' => 10,
                'page_section_id' => 4,
                'title' => 'Kelli Mumphries, Founder & CEO, MilkSpace',
                'sub_title' => null,
                'description' => 'As a Black Mom, I can’t emphasize enough how essential it is to have a supportive and relatable community. Community is important no matter where you are, and having access to a community that is a direct reflection of me is something I need and cherish at all times. Akina creates a space where I can connect with others who not only look like me but also share the same values while coming from different walks of life. Akina has become a place where I can truly connect, learn, and grow in my roles as a mother, wife, friend, and entrepreneur. Simply put, I am deeply grateful to have access to the Akina community.',
                'asset_type' => 'image',
                'asset_link' => 'https://akina-app-v2.s3.amazonaws.com/pages/assets/1728657430_kelli-mumphries-founder-ceo-milkspace-asset.jpg',
                'anchor_text' => null,
                'anchor_link' => null,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'id' => 11,
                'page_section_id' => 4,
                'title' => 'Dr. Melissa Robinson-Brown, Founder Renewed Focus Psychology Services',
                'sub_title' => null,
                'description' => 'Akina is revolutionary!  The mission to create a space for black women, regardless of the roles we play, to not only come together and create community, uplift one another, support one another but also to engage in self-improvement, find opportunities for growth professionally and personally, and just have a ton of fun is what we, as black women, deeply need.  The app features the ease of use, the ability to build connections with women all over the world (I think this is true), and the integration of AI is going to change the game for Black women.  And I’m  HERE FOR IT!',
                'asset_type' => 'image',
                'asset_link' => 'https://akina-app-v2.s3.amazonaws.com/pages/assets/1728657525_dr-melissa-robinson-brown-founder-renewed-focus-psychology-services-asset.jpg',
                'anchor_text' => null,
                'anchor_link' => null,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'id' => 12,
                'page_section_id' => 4,
                'title' => 'Kirsten Smith, Founder, MotherHues and BabyHues',
                'sub_title' => null,
                'description' => 'I am very excited about Akina 2.0! The previous app allowed me to network with other black women in a space where I felt comfortable discussing a variety of topics and supporting other black businesses.',
                'asset_type' => 'image',
                'asset_link' => 'https://akina-app-v2.s3.amazonaws.com/pages/assets/1728657582_kirsten-smith-founder-motherhues-and-babyhues-asset.jpg',
                'anchor_text' => null,
                'anchor_link' => null,
                'created_at' => $now,
                'updated_at' => $now,
            ],
        ]);
    }
}
