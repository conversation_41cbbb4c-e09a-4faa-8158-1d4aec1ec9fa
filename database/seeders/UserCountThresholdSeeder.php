<?php

namespace Database\Seeders;

use App\Models\UserCountThreshold;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Carbon;

class UserCountThresholdSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        UserCountThreshold::truncate();
        $now = Carbon::now();

        UserCountThreshold::insert([
            [
                'id' => 1,
                'allowed_user_count' => 1000,
                'created_at' => $now,
                'updated_at' => $now,
            ],
        ]);
    }
}
