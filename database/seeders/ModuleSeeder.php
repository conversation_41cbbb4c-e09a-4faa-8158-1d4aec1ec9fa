<?php

namespace Database\Seeders;

use App\Enums\ModuleType;
use App\Models\Module;
use App\Models\ModuleMeta;
use App\Models\Music;
use App\Models\Tag;
use Carbon\Carbon;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Schema;

class ModuleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $now = Carbon::now();

        $modules = [
            [
                'id' => 1,
                'title' => 'Motherhood',
                'slug' => 'maternal-health-motherhood',
                'thumbnail' => 'https://akina-app-v2.s3.amazonaws.com/cover_images/mother-hood-header.jpeg',
                'cover_image' => 'https://akina-app-v2.s3.amazonaws.com/post_photos/1727959672_test-post11-thumbnail.jpeg',
                'mob_image' => 'https://akina-app-v2.s3.us-east-1.amazonaws.com/akina_hub/motherhood.png',
                'web_image' => null,
                'cover_image_title' => 'Your motherhood journey starts here.',
                'order' => 1,
                'type' => ModuleType::BLOG->value,
                'logo_color' => '#FB0238',
                'bg_color' => '#ACD3E5',
                'header_content_value' => 'https://akina-app-v2.s3.amazonaws.com/videos/Motherhood.mp4',
                'header_content_type' => 'video',
                'description' => "From expert advice on maternal health, prenatal and postnatal care, to discussions on\nnavigating motherhood, our goal is to empower and uplift. Join us to access valuable\ninformation, share experiences, and connect with other moms on this incredible journey.",
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'id' => 2,
                'title' => 'Entrepreneurship',
                'slug' => 'entrepreneurship-career-development',
                'thumbnail' => 'https://akina-app-v2.s3.amazonaws.com/cover_images/entrepreneur-header.jpeg',
                'cover_image' => 'https://akina-app-v2.s3.amazonaws.com/post_photos/1727959828_test-post111-thumbnail.jpeg',
                'mob_image' => 'https://akina-app-v2.s3.us-east-1.amazonaws.com/akina_hub/entrepreneurship.png',
                'web_image' => null,
                'cover_image_title' => 'Black women leading in start ups',
                'order' => 2,
                'type' => ModuleType::BLOG->value,
                'logo_color' => '#FFF498',
                'bg_color' => '#FBF59D',
                'header_content_value' => 'https://akina-app-v2.s3.amazonaws.com/videos/entrepeneurship-supporting-your-dreams.mp4',
                'header_content_type' => 'video',
                'description' => 'Dedicated to empowering Black women in their professional journeys. From launching a\nbusiness to advancing in corporate settings, find expert insights, success stories, and\npractical tips to achieve your career goals.',
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'id' => 3,
                'title' => 'Mental Health',
                'slug' => 'mental-health-wellness',
                'thumbnail' => 'https://akina-app-v2.s3.amazonaws.com/cover_images/mental-health-header.jpeg',
                'cover_image' => 'https://akina-app-v2.s3.amazonaws.com/post_photos/1727959884_test-post11111-thumbnail.jpg',
                'mob_image' => 'https://akina-app-v2.s3.us-east-1.amazonaws.com/akina_hub/mental-Health.png',
                'web_image' => null,
                'cover_image_title' => 'Empower Your Mind, Nurture Your Soul: Exploring Mental Wellness',
                'order' => 3,
                'type' => ModuleType::BLOG->value,
                'logo_color' => '#1B9C5C',
                'bg_color' => '#99B5B1',
                'header_content_value' => 'https://akina-app-v2.s3.amazonaws.com/videos/mental-health-video.mp4',
                'header_content_type' => 'video',
                'description' => "We prioritize the emotional and mental well-being of Black women by offering a wealth of\nresources, expert advice, and supportive community spaces. Explore articles,\nworkshops, and personal stories that address unique challenges, promote self-care, and\nfoster resilience. Join us on a journey toward holistic wellness, where your mental health\nis nurtured and celebrated.",
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'id' => 4,
                'title' => 'Music',
                'slug' => 'music',
                'thumbnail' => 'https://akina-app-v2.s3.amazonaws.com/cover_images/music-header.jpeg',
                'cover_image' => null,
                'mob_image' => 'https://akina-app-v2.s3.us-east-1.amazonaws.com/akina_hub/music.png',
                'web_image' => null,
                'cover_image_title' => null,
                'order' => 4,
                'type' => ModuleType::MUSIC->value,
                'logo_color' => '#FFF498',
                'bg_color' => '#FB0238',
                'header_content_value' => 'https://akina-app-v2.s3.amazonaws.com/music_photos/music-header.png',
                'header_content_type' => 'image',
                'description' => "Dive into the vibrant world of music with our curated playlists celebrating the rich\ndiversity of the diaspora. From Afrobeat rhythms to soulful melodies, explore a tapestry of\nsounds of inspiration, relaxation, or a soundtrack for your day, our music section offers a\nsoul-stirring journey through the beats of our heritage.",
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'id' => 5,
                'title' => 'Videos',
                'slug' => 'videos',
                'thumbnail' => 'https://akina-app-v2.s3.amazonaws.com/cover_images/video-header.jpeg',
                'cover_image' => null,
                'mob_image' => 'https://akina-app-v2.s3.us-east-1.amazonaws.com/akina_hub/videos.png',
                'web_image' => null,
                'cover_image_title' => null,
                'order' => 5,
                'type' => ModuleType::VIDEO->value,
                'logo_color' => '#FFF498',
                'bg_color' => '#1B9C5C',
                'header_content_value' => 'https://akina-app-v2.s3.amazonaws.com/video_videos/1730813707_how-to-teach-yourself-to-be-financially-literate-x-sarah-jakes-roberts-caline-newton-video.mp4',
                'header_content_type' => 'video',
                'description' => null,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'id' => 6,
                'title' => 'EmpowerHer Perks',
                'slug' => 'empowerher-perks',
                'thumbnail' => 'https://akina-app-v2.s3.amazonaws.com/cover_images/empower-her-header.jpeg',
                'cover_image' => null,
                'mob_image' => 'https://akina-app-v2.s3.us-east-1.amazonaws.com/akina_hub/entrepreneurship.png',
                'web_image' => null,
                'cover_image_title' => null,
                'order' => 6,
                'type' => ModuleType::EVENT->value,
                'logo_color' => '#FFF498',
                'bg_color' => '#1B9C5C',
                'header_content_value' => 'https://akina-app-v2.s3.amazonaws.com/cover_images/empower-her-header.jpeg',
                'header_content_type' => 'image',
                'description' => null,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'id' => 7,
                'title' => 'Podcast',
                'slug' => 'podcast',
                'thumbnail' => 'https://akina-app-v2.s3.amazonaws.com/cover_images/podcasts-header-new.jpeg',
                'cover_image' => null,
                'mob_image' => 'https://akina-app-v2.s3.us-east-1.amazonaws.com/akina_hub/podcasts.png',
                'web_image' => null,
                'cover_image_title' => null,
                'order' => 7,
                'type' => ModuleType::PODCAST->value,
                'logo_color' => '#FBF59D',
                'bg_color' => '#FBF59D',
                'header_content_value' => 'some_value',
                'header_content_type' => 'image',
                'description' => "Discover a rich tapestry of voices and stories with our curated selection of podcasts\ncovering diverse genres and topics. From entrepreneurship to mental health to current\nevents, our podcast section offers a captivating auditory experience tailored to entertain\nand empower Black women.",
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'id' => 8,
                'title' => 'News',
                'slug' => 'news',
                'thumbnail' => 'https://akina-app-v2.s3.amazonaws.com/cover_images/news-header-new.jpeg',
                'cover_image' => null,
                'mob_image' => 'https://akina-app-v2.s3.us-east-1.amazonaws.com/akina_hub/entrepreneurship.png',
                'web_image' => null,
                'cover_image_title' => null,
                'order' => 8,
                'type' => ModuleType::COMING_SOON->value,
                'logo_color' => '#FB0238',
                'bg_color' => '#FB0238',
                'header_content_value' => 'some_value',
                'header_content_type' => 'image',
                'description' => "Stay informed and empowered with our curated news section, bringing you the latest\nupdates on current events from a variety of trusted media sources. From politics to\nculture, our diverse selection ensures you're up-to-date on the stories shaping our world.",
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'id' => 9,
                'title' => 'Events',
                'slug' => 'events',
                'thumbnail' => 'https://akina-app-v2.s3.amazonaws.com/cover_images/events-header-new.jpeg',
                'cover_image' => null,
                'mob_image' => 'https://akina-app-v2.s3.us-east-1.amazonaws.com/akina_hub/entrepreneurship.png',
                'web_image' => null,
                'cover_image_title' => null,
                'order' => 9,
                'type' => ModuleType::EVENT_NEW->value,
                'logo_color' => '#FFFFFF',
                'bg_color' => '#FBF59D',
                'header_content_value' => 'https://akina-app-v2.s3.amazonaws.com/cover_images/events-header-image.png',
                'header_content_type' => 'image',
                'description' => 'Communities where black women can connect, share experiences, and build strong bonds, fostering a sense of sisterhood and solidarity.',
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'id' => 10,
                'title' => 'The Culture Club',
                'slug' => 'the-culture-club',
                'thumbnail' => 'https://akina-app-v2.s3.amazonaws.com/cover_images/cultural-hub-header-image.jpeg',
                'cover_image' => null,
                'mob_image' => 'https://akina-app-v2.s3.us-east-1.amazonaws.com/akina_hub/entrepreneurship.png',
                'web_image' => null,
                'cover_image_title' => null,
                'order' => 9,
                'type' => ModuleType::COMING_SOON->value,
                'logo_color' => '#1B9C5C',
                'bg_color' => '#FF6C52',
                'header_content_value' => 'https://akina-app-v2.s3.amazonaws.com/cover_images/events-header-image.png',
                'header_content_type' => 'image',
                'description' => 'Platforms dedicated to discussing and celebrating diverse beauty standards, fashion, and beauty tips tailored to black women.',
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'id' => 11,
                'title' => 'Ask Akina',
                'slug' => 'ask-akina',
                'thumbnail' => 'https://akina-app-v2.s3.amazonaws.com/post_photos/1731418667_akina-ai-thumbnail.jpeg',
                'cover_image' => null,
                'mob_image' => 'https://akina-app-v2.s3.us-east-1.amazonaws.com/akina_hub/entrepreneurship.png',
                'web_image' => null,
                'cover_image_title' => null,
                'order' => 9,
                'type' => ModuleType::ASK_AKINA->value,
                'logo_color' => '#FFFFFF',
                'bg_color' => '#000000',
                'header_content_value' => 'https://akina-app-v2.s3.amazonaws.com/cover_images/events-header-image.png',
                'header_content_type' => 'image',
                'description' => 'Platforms dedicated to discussing and celebrating diverse beauty standards, fashion, and beauty tips tailored to black women.',
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'id' => 12,
                'title' => 'Mentorship',
                'slug' => 'mentorship',
                'thumbnail' => 'https://akina-app-v2.s3.amazonaws.com/cover_images/mentroship-header-image.jpeg',
                'cover_image' => null,
                'mob_image' => 'https://akina-app-v2.s3.us-east-1.amazonaws.com/akina_hub/entrepreneurship.png',
                'web_image' => null,
                'cover_image_title' => null,
                'order' => 9,
                'type' => ModuleType::COMING_SOON->value,
                'logo_color' => '#FBF59D',
                'bg_color' => '#FBF59D',
                'header_content_value' => 'https://akina-app-v2.s3.amazonaws.com/cover_images/events-header-image.png',
                'header_content_type' => 'image',
                'description' => 'Platforms dedicated to tips tailored to black women.',
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'id' => 13,
                'title' => 'Social Connect',
                'slug' => 'social-connect',
                'thumbnail' => 'https://akina-app-v2.s3.amazonaws.com/post_photos/1732873574_test-post-thumbnail.jpeg',
                'cover_image' => null,
                'mob_image' => 'https://akina-app-v2.s3.us-east-1.amazonaws.com/akina_hub/entrepreneurship.png',
                'web_image' => null,
                'cover_image_title' => null,
                'order' => 9,
                'type' => ModuleType::SOCIAL_CONNECT->value,
                'logo_color' => '#67AAB3',
                'bg_color' => '#67AAB3',
                'header_content_value' => 'https://akina-app-v2.s3.amazonaws.com/cover_images/events-header-image.png',
                'header_content_type' => 'image',
                'description' => 'Communities where black women can connect, share experiences, and build strong bonds, fostering a sense of sisterhood and solidarity.',
                'created_at' => $now,
                'updated_at' => $now,
            ],
        ];

        // Disable foreign key checks temporarily
        Schema::disableForeignKeyConstraints();

        Module::truncate();

        Schema::enableForeignKeyConstraints();

        Module::insert($modules);
    }
}
