<?php

namespace Database\Seeders;

use App\Models\PageSection;
use Carbon\Carbon;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Schema;

class PageSectionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        Schema::disableForeignKeyConstraints();
        PageSection::truncate();
        $now = Carbon::now();
        Schema::enableForeignKeyConstraints();

        PageSection::insert([
            ['id' => 1, 'type' => 'header_section', 'page_id' => 1, 'created_at' => $now, 'updated_at' => $now],
            ['id' => 2, 'type' => 'info_section', 'page_id' => 1, 'created_at' => $now, 'updated_at' => $now],
            ['id' => 3, 'type' => 'modules_section', 'page_id' => 1, 'created_at' => $now, 'updated_at' => $now],
            ['id' => 4, 'type' => 'customer_reviews', 'page_id' => 1, 'created_at' => $now, 'updated_at' => $now],
        ]);
    }
}
