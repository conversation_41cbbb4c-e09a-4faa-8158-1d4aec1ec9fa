<?php

namespace Database\Seeders;

use App\Models\Page;
use Carbon\Carbon;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Schema;

class PagesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        Schema::disableForeignKeyConstraints();
        Page::truncate();
        $now = Carbon::now();
        Schema::enableForeignKeyConstraints();

        Page::insert([
            [
                'id' => 1,
                'title' => 'Home',
                'slug' => 'home',
                'created_at' => $now,
                'updated_at' => $now,
            ],
        ]);
    }
}
