<?php

namespace Database\Seeders;

use App\Models\ModuleMeta;
use Carbon\Carbon;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ModuleMetaSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $now = Carbon::now();

        $moduleMetas = [
            [
                'id' => 1,
                'module_id' => 1,
                'type' => 'header',
                'title' => 'Motherhood connection.',
                'description' => null,
                'powered_by_text' => 'Powered by Mae Health',
                'powered_by_link' => 'http://www.meetmae.com/',
                'powered_by_logo' => 'https://akina-app-v2.s3.amazonaws.com/pages/assets/1728047191_test-post-asset.png',
                'akina_logo' => 'https://akina-app-v2.s3.amazonaws.com/pages/assets/1728047150_test-post-asset.png',
                'bg_image' => 'https://akina-app-v2.s3.amazonaws.com/post_photos/1739279478_test-post-thumbnail.png',
                'slider_images' => null,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'id' => 2,
                'module_id' => 1,
                'type' => 'profile_section',
                'title' => 'Even more reasons to complete your profile',
                'description' => "By completing your profile, you'll unlock personalized recommendations and content that align with your interests, goals, and aspirations, enhancing your experience within our community. Sharing your preferences and background empowers us to better understand your needs, enabling us to provide tailored resources and support for your journey.\n\n",
                'powered_by_text' => null,
                'powered_by_link' => null,
                'powered_by_logo' => null,
                'akina_logo' => 'https://akina-app-v2.s3.amazonaws.com/pages/assets/1728047254_test-post-asset.png',
                'bg_image' => 'https://akina-app-v2.s3.amazonaws.com/post_photos/1729592963_test-post-thumbnail.png',
                'slider_images' => null,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'id' => 3,
                'module_id' => 2,
                'type' => 'header',
                'title' => 'Success at your level.',
                'description' => null,
                'powered_by_text' => 'Powered by Official Black Wall Street',
                'powered_by_link' => 'http://www.obws.com/',
                'powered_by_logo' => 'https://akina-app-v2.s3.amazonaws.com/post_photos/1728395637_test-post-thumbnail.png',
                'akina_logo' => 'https://akina-app-v2.s3.amazonaws.com/post_photos/1728395572_test-post-thumbnail.png',
                'bg_image' => 'https://akina-app-v2.s3.amazonaws.com/post_photos/1728395525_test-post-thumbnail.png',
                'slider_images' => null,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'id' => 4,
                'module_id' => 2,
                'type' => 'profile_section',
                'title' => 'Even more reasons to complete your profile',
                'description' => 'Completing your Akina profile can be a powerful way for building your personal or business brand, connecting with others. ',
                'powered_by_text' => null,
                'powered_by_link' => null,
                'powered_by_logo' => null,
                'akina_logo' => 'https://akina-app-v2.s3.amazonaws.com/post_photos/1728395725_test-post-thumbnail.png',
                'bg_image' => 'https://akina-app-v2.s3.amazonaws.com/post_photos/1728395755_test-post-thumbnail.png',
                'slider_images' => null,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'id' => 5,
                'module_id' => 3,
                'type' => 'header',
                'title' => 'Mental Health Wellness',
                'description' => null,
                'powered_by_text' => 'Powered by BlackFemaleTherapists',
                'powered_by_link' => 'https://www.blackfemaletherapists.com/',
                'powered_by_logo' => 'https://akina-app-v2.s3.amazonaws.com/post_photos/1728396549_test-post-thumbnail.png',
                'akina_logo' => 'https://akina-app-v2.s3.amazonaws.com/post_photos/1728396607_test-post-thumbnail.png',
                'bg_image' => 'https://akina-app-v2.s3.amazonaws.com/post_photos/1739279723_test-post-thumbnail.png',
                'slider_images' => null,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'id' => 6,
                'module_id' => 3,
                'type' => 'profile_section',
                'title' => 'Even more reasons to complete your profile',
                'description' => 'Ensure that you receive personalized recommendations and tailored content that resonates with your interests, goals, and aspirations, creating a more enriching and fulfilling experience within Akina. By sharing your preferences and background, you empower us to better understand your needs and provide the resources and support that best align with your journey.',
                'powered_by_text' => null,
                'powered_by_link' => null,
                'powered_by_logo' => null,
                'akina_logo' => 'https://akina-app-v2.s3.amazonaws.com/post_photos/1728396673_test-post-thumbnail.png',
                'bg_image' => 'https://akina-app-v2.s3.amazonaws.com/post_photos/1729593212_test-post-thumbnail.png',
                'slider_images' => null,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'id' => 7,
                'module_id' => 7,
                'type' => 'sllider',
                'title' => 'Podcast Image Slider',
                'description' => 'Podcast Image Slider',
                'powered_by_text' => null,
                'powered_by_link' => null,
                'powered_by_logo' => null,
                'akina_logo' => null,
                'bg_image' => null,
                'slider_images' => json_encode([
                    ["image" => "https://akina-app-v2.s3.amazonaws.com/post_photos/1731570357_podcast-slider-thumbnail.png"],
                    ["image" => "https://akina-app-v2.s3.amazonaws.com/post_photos/1731570492_podcast-slider-thumbnail.png"],
                    ["image" => "https://akina-app-v2.s3.amazonaws.com/post_photos/1731570515_podcast-slider-thumbnail.png"],
                    ["image" => "https://akina-app-v2.s3.amazonaws.com/post_photos/1731570651_podcast-slider-thumbnail.png"],
                    ["image" => "https://akina-app-v2.s3.amazonaws.com/post_photos/1731570678_podcast-slider-thumbnail.png"]
                ]),
                'created_at' => $now,
                'updated_at' => $now,
            ],
        ];

        ModuleMeta::truncate();

        ModuleMeta::insert($moduleMetas);
    }
}
