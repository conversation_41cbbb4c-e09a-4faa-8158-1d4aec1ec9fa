<?php

namespace Database\Seeders;

use App\Models\Organization;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Carbon;

class OrganizationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        Organization::truncate();
        $now = Carbon::now();

        Organization::insert([
            ['id' => 1, 'name' => 'Manifest Your Purpose', 'is_active' => 1, 'created_at' => $now, 'updated_at' => $now],
            ['id' => 2, 'name' => 'Black Woman On A Mission', 'is_active' => 1, 'created_at' => $now, 'updated_at' => $now],
            ['id' => 3, 'name' => 'MotherHue', 'is_active' => 1, 'created_at' => $now, 'updated_at' => $now],
            ['id' => 4, 'name' => 'Mansfield Funeral Home & Cremations', 'is_active' => 1, 'created_at' => $now, 'updated_at' => $now],
            ['id' => 5, 'name' => 'Stoop and Stank', 'is_active' => 1, 'created_at' => $now, 'updated_at' => $now],
            ['id' => 6, 'name' => 'Marketsetgolaw', 'is_active' => 1, 'created_at' => $now, 'updated_at' => $now],
            ['id' => 7, 'name' => 'Vali + Sella Swimwear', 'is_active' => 1, 'created_at' => $now, 'updated_at' => $now],
            ['id' => 8, 'name' => 'The Allignment Chapter', 'is_active' => 1, 'created_at' => $now, 'updated_at' => $now],
            ['id' => 9, 'name' => 'Meet Mae', 'is_active' => 1, 'created_at' => $now, 'updated_at' => $now],
            ['id' => 10, 'name' => 'Lindsay Butler', 'is_active' => 1, 'created_at' => $now, 'updated_at' => $now],
            ['id' => 11, 'name' => 'Christina Mullen', 'is_active' => 1, 'created_at' => $now, 'updated_at' => $now],
            ['id' => 12, 'name' => 'Other', 'is_active' => 1, 'created_at' => $now, 'updated_at' => $now],
        ]);
    }
}
