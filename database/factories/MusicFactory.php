<?php

namespace Database\Factories;

use App\Enums\ModuleType;
use App\Models\Module;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Music>
 */
class MusicFactory extends Factory
{

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'title' => fake()->title(),
            'audio_link' => fake()->imageUrl(),
            'thumbnail' => fake()->imageUrl(),
            'module_id' => Module::where('type', ModuleType::MUSIC->value)->first()->id,
        ];
    }
}
