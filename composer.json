{"name": "laravel/laravel", "type": "project", "description": "Akina connect V2 backend API engine.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.2", "darkaonline/l5-swagger": "^8.6", "kreait/firebase-php": "^7.16", "kreait/laravel-firebase": "^5.10", "laravel/cashier": "^15.6", "laravel/framework": "^11.0", "laravel/reverb": "^1.0", "laravel/sanctum": "^4.0", "laravel/tinker": "^2.9", "league/flysystem-aws-s3-v3": "^3.27", "mailersend/laravel-driver": "^2.4", "openai-php/client": "^0.10.1", "pusher/pusher-php-server": "^7.2", "sentry/sentry-laravel": "^4.13", "spatie/laravel-responsecache": "^7.7", "ext-redis": "*"}, "require-dev": {"beyondcode/laravel-query-detector": "^2.1", "fakerphp/faker": "^1.23", "laravel/pint": "^1.13", "laravel/sail": "^1.26", "laravel/telescope": "^5.7", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.0", "pestphp/pest": "^2.34", "pestphp/pest-plugin-laravel": "^2.3", "spatie/laravel-ignition": "^2.4"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"]}, "extra": {"laravel": {"dont-discover": ["laravel/telescope"]}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}