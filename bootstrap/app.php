<?php

use App\Exceptions\RenderModelNotFoundException;
use App\Http\Middleware\JsonOnly;
use App\Http\Middleware\PlatformMiddleware;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use Illuminate\Support\Facades\Route;
use Laravel\Sanctum\Http\Middleware\CheckAbilities;
use Laravel\Sanctum\Http\Middleware\CheckForAnyAbility;
use Sentry\Laravel\Integration;
use Spatie\ResponseCache\Middlewares\CacheResponse;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__ . '/../routes/web.php',
        api: __DIR__ . '/../routes/api.php',
        commands: __DIR__ . '/../routes/console.php',
        health: '/up',
        then: function () {
            Route::middleware('api')
                ->prefix('api/v1')->group(function () {

                    Route::name('guest.')
                        ->group(base_path('routes/v1/guest.php'));

                    Route::prefix('admin')
                        ->name('admin.')
                        ->group(base_path('routes/admin.php'));

                    Route::middleware(['api', 'auth:sanctum'])->group(function () {

                        Route::prefix('')
                            ->group(base_path('routes/v1/general.php'));

                        Route::prefix('')
                            ->group(base_path('routes/v1/report_content.php'));

                        Route::name('social_connect.')
                            ->group(base_path('routes/v1/social_connect.php'));

                        Route::name('push_notification.')
                            ->group(base_path('routes/v1/push_notification.php'));

                        Route::prefix('')
                            ->group(base_path('routes/v1/user.php'));

                        Route::prefix('modules/{module_id}/posts')
                            ->group(base_path('routes/v1/posts.php'));

                        Route::prefix('user-post-requests')
                            ->group(base_path('routes/v1/user_post_requests.php'));

                        Route::prefix('modules/{module_id}/akina-ai')
                            ->group(base_path('routes/v1/akina-ai.php'));

                        Route::prefix('modules/{module_id}/music')
                            ->group(base_path('routes/v1/music.php'));

                        Route::prefix('modules/{module_id}/podcasts')
                            ->group(base_path('routes/v1/podcasts.php'));

                        Route::prefix('stripe')->name('stripe.')
                            ->group(base_path('routes/v1/stripe.php'));
                    });
                });

        },
    )
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->validateCsrfTokens(except: [
            'stripe/*',
        ]);
        $middleware->append([JsonOnly::class, PlatformMiddleware::class]);
        $middleware->alias([
            'abilities' => CheckAbilities::class,
            'ability' => CheckForAnyAbility::class,
            'cache.response' => CacheResponse::class,

        ]);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        Integration::handles($exceptions);
        $exceptions->render(new RenderModelNotFoundException());
    })->create();
