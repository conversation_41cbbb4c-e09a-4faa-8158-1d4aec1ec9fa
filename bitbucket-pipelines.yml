image: atlassian/default-image:2

pipelines:
  branches:
    development:
      - step:
          script:
            - apt-get update
            - apt-get install -y zip
            - zip -r application2.zip . -x "vendor/*"
            - pipe: atlassian/aws-code-deploy:1.5.0
              variables:
                AWS_DEFAULT_REGION: $AWS_DEFAULT_REGION
                AWS_ACCESS_KEY_ID: $AWS_ACCESS_KEY_ID
                AWS_SECRET_ACCESS_KEY: $AWS_SECRET_ACCESS_KEY
                APPLICATION_NAME: $APPLICATION_NAME
                S3_BUCKET: $S3_BUCKET
                DEBUG: 'true'
                COMMAND: 'upload'
                ZIP_FILE: 'application2.zip'
                VERSION_LABEL: 'my-app-1.1.1'
            - pipe: atlassian/aws-code-deploy:1.5.0
              variables:
                AWS_DEFAULT_REGION: $AWS_DEFAULT_REGION
                AWS_ACCESS_KEY_ID: $AWS_ACCESS_KEY_ID
                AWS_SECRET_ACCESS_KEY: $AWS_SECRET_ACCESS_KEY
                APPLICATION_NAME: $APPLICATION_NAME
                S3_BUCKET: $S3_BUCKET
                DEBUG: 'true'
                DEPLOYMENT_GROUP: 'laravelV2DeploymentGroup'
                COMMAND: 'deploy'
                WAIT: 'true'
                VERSION_LABEL: 'my-app-1.1.1'
                IGNORE_APPLICATION_STOP_FAILURES: 'true'
                FILE_EXISTS_BEHAVIOR: 'OVERWRITE'
