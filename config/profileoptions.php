<?php


return [
    'education_level' => [
        ['name' => 'high_school_diploma', 'value' => "High School Diploma"],
        ['name' => 'associate_degree', 'value' => "Associate’s Degree"],
        ['name' => 'some_college', 'value' => "Some College"],
        ['name' => 'bachelor_degree', 'value' => "Bachelor's Degree"],
        ['name' => 'master_degree', 'value' => "Master’s Degree"],
        ['name' => 'professional_degree', 'value' => "Professional Degree"],
    ],
    'income_level' => [
        ['name' => 'under_25000', 'value' => "Under $25,000"],
        ['name' => '25000_49999', 'value' => "$25,000 - $49,999 "],
        ['name' => '50000_74999', 'value' => "$50,000 - $74,999"],
        ['name' => '75000_99999', 'value' => "$75,000 - $99,999"],
        ['name' => '100000_149999', 'value' => "$100,000 - $149,999"],
        ['name' => '150000_199999', 'value' => "$150,000 - $199,999"],
        ['name' => '200000_249999', 'value' => "$200,000 - $249,999"],
        ['name' => '250000_and_above', 'value' => "$250,000 and above"],
        ['name' => 'prefer_not_to_say', 'value' => "Prefer not to say"],
    ],
    'marital_status' => [
        ['name' => 'single', 'value' => "Single"],
        ['name' => 'married', 'value' => "Married"],
        ['name' => 'divorced', 'value' => "Divorced"],
        ['name' => 'widowed', 'value' => "Widowed"],
        ['name' => 'prefer_not_to_say', 'value' => "Prefer not to say"],
    ],
    'children_option' => [
        ['name' => 'no', 'value' => "No"],
        ['name' => 'yes_1_3', 'value' => "Yes, 1-3"],
        ['name' => 'yes_4_5', 'value' => "Yes, 4-5"],
        ['name' => 'yes_6_or_more', 'value' => "Yes, 6 or more"],
    ],
    'interests_option' => [
        ['name' => 'entrepreneurship', 'value' => "Entrepreneurship"],
        ['name' => 'professional_career_development', 'value' => "Professional/Career Development"],
        ['name' => 'maternal_health', 'value' => "Maternal Health"],
        ['name' => 'mental_health', 'value' => "Mental Health"],
        ['name' => 'mentorship', 'value' => "Mentorship"],
        ['name' => 'classes', 'value' => "Classes"],
        ['name' => 'social_events', 'value' => "Social Events"],
        ['name' => 'networking_events', 'value' => "Networking Events"],
        ['name' => 'special_benefits_to_retailers_services', 'value' => "Special Benefits to Retailers/Services"],
    ],
    'joining_motivation_options' => [
        ['name' => 'looking_to_expand_my_professional_network', 'value' => "Looking to expand my professional network"],
        ['name' => 'looking_to_expand_my_social_network', 'value' => "Looking to expand my social network"],
        ['name' => 'resources_information_on_maternal_health', 'value' => "Resources / Information on maternal health"],
        ['name' => 'resources_information_on_mental_health', 'value' => "Resources / Information on mental health"],
        ['name' => 'connecting_virtually_with_other_black_women', 'value' => "Connecting virtually with other Black women"],
        ['name' => 'connecting_in_person_with_other_black_women', 'value' => "Connecting in-person with other Black women"],

    ]
];
