{"openapi": "3.0.0", "info": {"title": "Akina connect API Documentation", "description": "Akina connect Api documentation.", "contact": {"email": "<EMAIL>"}, "version": "1.0"}, "servers": [{"url": "/api/v1"}], "paths": {"/register": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "User Registration", "description": "User Registration", "operationId": "registerUser", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["first_name", "last_name", "password", "email"], "properties": {"first_name": {"type": "string", "format": "string", "example": "<PERSON><PERSON>"}, "last_name": {"type": "string", "format": "string", "example": "<PERSON><PERSON><PERSON>"}, "password": {"type": "password", "format": "string", "example": "ansar@786"}, "email": {"type": "email", "format": "string", "example": "<EMAIL>"}}, "type": "object"}}}}, "responses": {"200": {"description": "Example of Success response", "content": {"application/json": {"schema": {"properties": {"success": {"type": "booleon", "example": "true"}, "message": {"type": "string", "example": "User registered successfully. Please check your mailbox to verify email address."}, "user": {"type": "object", "example": "{'first_name': '<PERSON><PERSON>','last_name': '<PERSON><PERSON><PERSON>','email': '<EMAIL>','id': 2  }"}}, "type": "object"}}}}, "422": {"description": "Example of Bad Request", "content": {"application/json": {"schema": {"properties": {"errors": {"type": "array", "items": {"properties": {"email": {"type": "string", "example": "The email has already been taken."}}, "type": "object"}}}, "type": "object"}}}}}}}, "/verify": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "Verify user email", "description": "Verify user email", "operationId": "verifyUserEmail", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["password", "email"], "properties": {"otp": {"type": "string", "format": "string", "example": "3506"}, "email": {"type": "email", "format": "string", "example": "<EMAIL>"}}, "type": "object"}}}}, "responses": {"200": {"description": "Example of Success response", "content": {"application/json": {"schema": {"properties": {"success": {"type": "booleon", "example": "true"}, "message": {"type": "string", "example": "User registered successfully. Please check your mailbox to verify email address."}, "user": {"type": "object", "example": "{'first_name': '<PERSON><PERSON>','last_name': '<PERSON><PERSON><PERSON>','email': '<EMAIL>','id': 2  }"}}, "type": "object"}}}}, "401": {"description": "Example of Bad Request", "content": {"application/json": {"schema": {"properties": {"success": {"type": "booleon", "example": "false"}, "message": {"type": "string", "example": "OTP is incorrect"}}, "type": "object"}}}}}}}, "/login": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "User Login", "description": "User Login", "operationId": "loginUser", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["password", "email"], "properties": {"password": {"type": "password", "format": "string", "example": "ansar@786"}, "email": {"type": "email", "format": "string", "example": "<EMAIL>"}}, "type": "object"}}}}, "responses": {"200": {"description": "Example of Success response", "content": {"application/json": {"schema": {"properties": {"success": {"type": "booleon", "example": "true"}, "message": {"type": "string", "example": "User registered successfully. Please check your mailbox to verify email address."}, "user": {"type": "object", "example": "{'first_name': '<PERSON><PERSON>','last_name': '<PERSON><PERSON><PERSON>','email': '<EMAIL>','id': 2  }"}}, "type": "object"}}}}, "401": {"description": "Example of Bad Request", "content": {"application/json": {"schema": {"properties": {"success": {"type": "booleon", "example": "false"}, "is_verified": {"type": "booleon", "example": "false"}, "message": {"type": "string", "example": "v"}}, "type": "object"}}}}}}}, "/modules": {"get": {"tags": ["<PERSON><PERSON><PERSON>"], "summary": "Get modules", "description": "Get modules", "operationId": "getModules", "responses": {"200": {"description": "Example of Success response", "content": {"application/json": {"schema": {"properties": {"success": {"type": "booleon", "example": "success"}, "data": {"type": "array", "items": {}, "example": [{"id": 1, "title": "<PERSON>", "thumbnail": "https://akinadev.s3.amazonaws.com/v2/images/mother-hood.jpeg", "order": 1, "type": "blog", "header_content_value": "video", "header_content_type": "dummy_url", "created_at": "2024-05-07T01:36:40.000000Z", "updated_at": "2024-05-07T01:36:40.000000Z"}, {"id": 1, "title": "<PERSON>", "thumbnail": "https://akinadev.s3.amazonaws.com/v2/images/mother-hood.jpeg", "order": 1, "type": "blog", "header_content_value": "video", "header_content_type": "dummy_url", "created_at": "2024-05-07T01:36:40.000000Z", "updated_at": "2024-05-07T01:36:40.000000Z"}]}}, "type": "object"}}}}, "401": {"description": "Example of Bad Request", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Unauthenticated"}}, "type": "object"}}}}}}}, "/get_profile": {"get": {"tags": ["User Profile"], "summary": "Get Profile", "description": "Get user profile selected options", "operationId": "getProfile", "responses": {"200": {"description": "Example of Success response", "content": {"application/json": {"schema": {"properties": {"success": {"type": "booleon", "example": "success"}, "user_profile1": {"type": "object", "example": [{"first_name": "<PERSON><PERSON>", "last_name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "profile_photo": false}]}, "education_level": {"type": "array", "items": {}, "example": [{"name": "high_school_diploma", "value": "High School Diploma", "selected": false}, {"name": "associate_degree", "value": "Associate’s Degree", "selected": true}, {"name": "some_college", "value": "Some College", "selected": false}]}, "income_level": {"type": "array", "items": {}, "example": [{"name": "under_25000", "value": "Under $25,000", "selected": false}, {"name": "25000_49999", "value": "$25,000 - $49,999", "selected": true}, {"name": "75000_99999", "value": "$75,000 - $99,999", "selected": false}]}}, "type": "object"}}}}, "401": {"description": "Example of Bad Request", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Unauthenticated"}}, "type": "object"}}}}}}}}, "components": {"securitySchemes": {"sanctum": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "Enter access token in format ( Bear<PERSON> {access_token} )", "name": "Authorization", "in": "header"}}}, "security": [{"sanctum": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "Enter access token in format ( Bear<PERSON> {access_token} )", "name": "Authorization", "in": "header"}}]}