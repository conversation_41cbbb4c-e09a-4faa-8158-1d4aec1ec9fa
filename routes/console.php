<?php

use App\Console\Commands\DispatchApiLimitCleanup;
use Illuminate\Foundation\Inspiring;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schedule;

Artisan::command('inspire', function () {
    $this->comment(Inspiring::quote());
})->purpose('Display an inspiring quote')->hourly();

Schedule::command('podcasts:import-podcasts')->dailyAt('00:01'); // Every day at 00:01
Schedule::command('news:import-news')->dailyAt('00:01'); // Every day at 00:01

Schedule::command(DispatchApiLimitCleanup::class)->weeklyOn(1, '1:00'); // Every Monday at 1:00 AM

