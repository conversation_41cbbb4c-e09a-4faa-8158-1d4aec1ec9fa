<?php

use App\Http\Controllers\PodCastCommentRepliesController;
use App\Http\Controllers\PodCastCommentsController;
use App\Http\Controllers\PodCastsController;
use Illuminate\Support\Facades\Route;


Route::get('/', [PodCastsController::class, 'index'])->name('get_podcasts');
Route::get('get-by-category/{category_id}', [PodCastsController::class, 'getByCategory'])->name('get_podcasts_by_category');
Route::get('search', [PodCastsController::class, 'search'])->name('search_podcasts');

Route::prefix('/{podcast_id}')->group(function () {

    Route::get('/', [PodCastsController::class, 'show'])->name('get_podcast_detail');
    Route::post('/like', [PodCastsController::class, 'like'])->name('like_podcast');
    Route::post('/bookmark', [PodCastsController::class, 'bookMark'])->name('bookmark_podcast');

    Route::prefix('/comments')->group(function () {

        Route::post('/', [PodCastCommentsController::class, 'store'])->name('add_podcast_comment');

        Route::prefix('/{comment_id}')->group(function () {

            Route::patch('', [PodCastCommentsController::class, 'update'])->name('edit_podcast_comment');
            Route::delete('', [PodCastCommentsController::class, 'destroy'])->name('delete_podcast_comment');

            Route::post('/replies', [PodCastCommentRepliesController::class, 'store'])->name('add_podcast_comment_reply');
            Route::patch('/replies/{reply_id}', [PodCastCommentRepliesController::class, 'update'])->name('edit_podcast_comment_reply');
            Route::delete('/replies/{reply_id}', [PodCastCommentRepliesController::class, 'destroy'])->name('delete_podcast_comment_reply');
        });
    });
});
