<?php

use App\Http\Controllers\AIConversationsController;
use App\Http\Controllers\AkinaAIController;
use App\Http\Controllers\AkinaAIConversationsController;
use Illuminate\Support\Facades\Route;


Route::get('/', [AkinaAIController::class, 'index'])->name('akina_ai');

Route::prefix('/conversation-streaming')->group(function () {
    Route::post('/', [AkinaAIConversationsController::class, 'conversationStream'])->name('akina_ai_conversation_stream');
});

Route::prefix('/conversations')->group(function () {
    Route::get('/', [AIConversationsController::class, 'index'])->name('get_akina_ai_conversations_list');
    Route::get('/{conversation_id}', [AIConversationsController::class, 'show'])->name('get_akina_ai_conversation');
    Route::delete('/{conversation_id}', [AIConversationsController::class, 'destroy'])->name('delete_akina_ai_conversation');
    Route::post('/', [AkinaAIConversationsController::class, 'store'])->name('akina_ai_conversation_socket');
});

Route::patch('messages/{message_id}/like', [AIConversationsController::class, 'likeMessage'])
    ->name('akina_ai_message_like');
