<?php

use App\Http\Controllers\AuthController;
use App\Http\Controllers\CategoriesController;
use App\Http\Controllers\InviteUsersController;
use App\Http\Controllers\LocationsController;
use App\Http\Controllers\PagesController;
use App\Http\Controllers\PageSectionDetailsController;
use App\Http\Controllers\PostsController;
use App\Http\Controllers\ReferralCodeController;
use App\Http\Controllers\ReportReasonsController;
use App\Http\Controllers\TagsController;
use App\Http\Controllers\TempAuthKeysController;
use Illuminate\Support\Facades\Route;


Route::post('logout', [AuthController::class, 'logOut'])->name('logout_user');
Route::get('categories', [CategoriesController::class, 'index'])->name('get_categories');
Route::resource('locations', LocationsController::class);
Route::resource('tags', TagsController::class);

Route::get('post-by-category/{category_id}', [PostsController::class, 'getByCategory'])->name('get_posts_by_category');

Route::get('pages', [PagesController::class, 'index'])->name('get_pages');
Route::get('pages/{slug}', [PagesController::class, 'show'])->name('get_page_details');
Route::post('pages/{page_id}/sections/{section_id}/details', [PageSectionDetailsController::class, 'store'])->name('add_page_section_details');

Route::post('invite-mass-users', [InviteUsersController::class, 'inviteMassUsers'])->name('invite_mass_users_send_email');

Route::get('temporary-auth/generate-key', [TempAuthKeysController::class, 'generateKey'])->name('generate_temp_auth_key');
Route::get('report-reasons', [ReportReasonsController::class, 'index'])->name('get_report_reasons');

Route::post('referral-codes', [ReferralCodeController::class, 'store'])->name('generate_referral_code');

