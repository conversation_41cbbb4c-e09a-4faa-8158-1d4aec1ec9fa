<?php

use App\Http\Controllers\BookMarksController;
use App\Http\Controllers\LikesController;
use App\Http\Controllers\ResetPasswordController;
use App\Http\Controllers\SubscriptionsController;
use App\Http\Controllers\UserNotificationPreferencesController;
use App\Http\Controllers\UserProfileController;
use App\Http\Controllers\UsersController;
use Illuminate\Support\Facades\Route;

Route::post('reset-password', [ResetPasswordController::class, 'resetPassword'])->name('reset_password');

Route::get('profile_options', [UserProfileController::class, 'options'])->name('get_profile_options');
Route::put('update_profile', [UserProfileController::class, 'update'])->name('update_user_profile');
Route::get('profile', [UserProfileController::class, 'show'])->name('get_user_simple_profile');
Route::get('get_profile', [UserProfileController::class, 'showV2'])->name('get_user_selected_profile');
Route::get('users/{user_id}/notification-preferences', [UserNotificationPreferencesController::class, 'show'])->name('get_user_notification_preferences');
Route::post('users/{user_id}/notification-preferences', [UserNotificationPreferencesController::class, 'update'])->name('update_user_notification_preferences');

Route::get('user-likes', [LikesController::class, 'index'])->name('get_user_likes');
Route::get('user-bookmarks', [BookMarksController::class, 'index'])->name('get_user_bookmarks');
Route::get('membership-details', [SubscriptionsController::class, 'getSubscriptionDetails'])->name('get_membership_details');
Route::delete('delete-account', [UsersController::class, 'destroy'])->name('delete_user_account');
