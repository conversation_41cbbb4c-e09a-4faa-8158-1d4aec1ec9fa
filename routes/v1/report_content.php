<?php

use App\Http\Controllers\BlockedUserController;
use App\Http\Controllers\ReportedCommentController;
use App\Http\Controllers\ReportedReplyController;

Route::post('report-comments/{comment_id}', [ReportedCommentController::class, 'store'])->name('report_comment');
Route::post('report-replies/{comment_reply_id}', [ReportedReplyController::class, 'store'])->name('report_reply');

Route::prefix('block-users')->group(function () {
    Route::get('', [BlockedUserController::class, 'index'])->name('get_blocked_users');
    Route::prefix('{user_id}')->group(function () {
        Route::post('', [BlockedUserController::class, 'store'])->name('block_a_user');
        Route::delete('', [BlockedUserController::class, 'destroy'])->name('unblock_a_user');
    });
});
