<?php

use App\Http\Controllers\AkinaAIConversationsSyncController;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\ConnectWithUsFormController;
use App\Http\Controllers\ForgotPasswordController;
use App\Http\Controllers\InviteUsersController;
use App\Http\Controllers\MusicController;
use App\Http\Controllers\NewsController;
use App\Http\Controllers\OrganizationController;
use App\Http\Controllers\OtpController;
use App\Http\Controllers\PlansController;
use App\Http\Controllers\PodCastsController;
use App\Http\Controllers\PostsController;
use App\Http\Controllers\SocialConnectPostsController;
use App\Http\Controllers\TempAuthKeysController;
use App\Http\Controllers\UserCountThresholdController;
use App\Http\Controllers\UserIOSBiometricController;
use App\Http\Controllers\UserProfileController;
use App\Http\Controllers\UserSupportController;
use App\Http\Controllers\VideosController;
use Illuminate\Support\Facades\Route;

Route::post('register-from-wp', [AuthController::class, 'registerFromWP'])->middleware('auth:sanctum')->name('register_from_wp');
Route::post('activate-wp-user', [AuthController::class, 'activateWpUser'])->middleware('auth:sanctum')->name('activate_wp_user');
Route::get('wp-users', [UserProfileController::class, 'showWpUsers'])->middleware('auth:sanctum')->name('get_wp_users');

//Route::middleware(['throttle:auth'])->group(function () {
Route::post('register', [AuthController::class, 'register'])->name('user_registration');
Route::post('verify', [AuthController::class, 'verify'])->name('verify_user');
Route::post('resend-otp', [OtpController::class, 'resendOtp'])->name('resend-otp');
Route::post('login', [AuthController::class, 'login'])->name('user_login');
Route::post('forgot-password', [ForgotPasswordController::class, 'forgotPassword'])->name('forgot_password');
Route::post('forgot-password-verify', [ForgotPasswordController::class, 'verify'])->name('forgot_password_verify');
Route::get('verify-user-externally', [AuthController::class, 'verifyUserExternally'])->name('verify_user_externally');
//});


Route::post('users/{user_id}/verify-ios-biometric', [UserIOSBiometricController::class, 'verify'])->name('verify_ios_biometric');

Route::get('support-form', [UserSupportController::class, 'index'])->name('get_support_form');
Route::post('support-form', [UserSupportController::class, 'store'])->name('set_support_form');

Route::get('connect-with-us-form', [ConnectWithUsFormController::class, 'index'])->name('get_connect_with_us_form');
Route::post('connect-with-us-form', [ConnectWithUsFormController::class, 'store'])->name('set_connect_with_us_form');

Route::post('temporary-auth/verify-key', [TempAuthKeysController::class, 'verifyKey'])->name('verify_temp_auth_key');

Route::get('plans', [PlansController::class, 'index'])->name('get_plans');
Route::get('organizations', [OrganizationController::class, 'index'])->name('get_organizations');
Route::get('is-registration-allowed', [UserCountThresholdController::class, 'index'])->name('is_registration_allowed');
Route::post('invite-users', [InviteUsersController::class, 'inviteUser'])->name('add_user_to_invite_list');

Route::prefix('guest/modules/{module_id}')->group(callback: function () {
    Route::get('posts/{post_id}', [PostsController::class, 'show'])->name('get_post_detail');
    Route::get('music/{music_id}', [MusicController::class, 'show'])->name('get_music_detail');
    Route::get('videos/{video_id}', [VideosController::class, 'show'])->name('get_video_detail');
    Route::get('podcasts/{podcast_id}', [PodCastsController::class, 'show'])->name('get_podcast_detail');
    Route::get('news/{news_id}', [NewsController::class, 'show'])->name('get_news_detail');
});
Route::get('guest/social-connect/posts/{id}', [SocialConnectPostsController::class, 'show'])->name('social_connect.get_sc_post_detail');

Route::post('modules/{module_id}/akina-ai/conversations-sync', [AkinaAIConversationsSyncController::class, 'store'])->name('akina_ai.sync_conversations');
Route::get('modules/{module_id}/akina-ai/conversations-sync/{external_conversation_id}', [AkinaAIConversationsSyncController::class, 'show'])->name('akina_ai.get_external_conversation_detail');
