<?php

use App\Http\Controllers\StripeWebhookController;
use App\Http\Controllers\SubscriptionsController;
use Illuminate\Support\Facades\Route;


Route::post('/setup-intent', [SubscriptionsController::class, 'createSetupIntent']);
Route::post('/subscribe', [SubscriptionsController::class, 'createSubscription']);
Route::post('/cancel-subscription', [SubscriptionsController::class, 'cancelSubscription']);
Route::get('/plans', [SubscriptionsController::class, 'plans']);
Route::post('webhook', [StripeWebhookController::class, 'handleWebhook'])
    ->name('webhook')->withoutMiddleware('auth:sanctum');
