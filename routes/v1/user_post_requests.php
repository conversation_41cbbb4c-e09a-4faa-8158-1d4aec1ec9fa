<?php

use App\Http\Controllers\UserPostRequestsController;
use Illuminate\Support\Facades\Route;


Route::get('/', [UserPostRequestsController::class, 'index'])->name('get_user_post_requests');
Route::post('', [UserPostRequestsController::class, 'store'])->name('add_user_post_request');

Route::prefix('/{user_post_request_id}')->group(function () {

    Route::get('/', [UserPostRequestsController::class, 'show'])->name('user_post_request_detail');
    Route::put('/', [UserPostRequestsController::class, 'update'])->name('edit_user_post_request');
    Route::delete('/', [UserPostRequestsController::class, 'destroy'])->name('delete_user_post_request');

});
