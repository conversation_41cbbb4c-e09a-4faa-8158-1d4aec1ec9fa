<?php

use App\Http\Controllers\SocialConnectReportedPostsController;
use App\Http\Controllers\SocialConnectPostBookMarksController;
use App\Http\Controllers\SocialConnectPostCommentRepliesController;
use App\Http\Controllers\SocialConnectPostCommentsController;
use App\Http\Controllers\SocialConnectPostLikesController;
use App\Http\Controllers\SocialConnectPostsController;
use App\Http\Controllers\UserFollowersController;
use App\Http\Controllers\UserFollowingsController;
use App\Http\Controllers\UsersController;
use Illuminate\Support\Facades\Route;

Route::prefix('social-connect')->group(function () {
    Route::get('users', [UsersController::class, 'index'])->name('get_users');
    Route::get('my-profile', [UsersController::class, 'profile'])->name('get_my_profile');
    Route::get('user-profile/{user_id}', [UsersController::class, 'profileDetails'])->name('get_other_user_profile');
    Route::post('search-users', [UsersController::class, 'search'])->name('search_users');
    Route::post('follow-user', [UserFollowersController::class, 'store'])->name('follow_user');

    Route::get('get-my-followers', [UserFollowersController::class, 'index'])->name('get_my_followers');
    Route::get('get-user-followers/{user_id}', [UserFollowersController::class, 'otherUsersFollowers'])->name('get_other_followers');

    Route::get('get-my-followings', [UserFollowingsController::class, 'index'])->name('get_my_followings');
    Route::get('get-user-followings/{user_id}', [UserFollowingsController::class, 'otherUsersFollowings'])->name('get_other_followings');

    Route::prefix('posts')->group(function () {
        Route::get('/', [SocialConnectPostsController::class, 'index'])->name('get_sc_posts');
        Route::get('/from-followers', [SocialConnectPostsController::class, 'indexFromFollowersOnly'])->name('get_sc_posts_from_followers');
        Route::get('/by-a-user/{user_id}', [SocialConnectPostsController::class, 'getPostsByUser'])->name('get_sc_posts_by_user');
        Route::post('/', [SocialConnectPostsController::class, 'store'])->name('add_sc_posts');
        Route::put('/{post_id}', [SocialConnectPostsController::class, 'update'])->name('edit_sc_posts');
        Route::delete('/{post_id}', [SocialConnectPostsController::class, 'destroy'])->name('delete_sc_posts');

        Route::prefix('{post_id}')->group(function () {
            Route::get('/', [SocialConnectPostsController::class, 'show'])->name('get_sc_post_detail');
            Route::post('/like', [SocialConnectPostLikesController::class, 'like'])->name('like_sc_post');
            Route::post('/bookmark', [SocialConnectPostBookMarksController::class, 'bookmark'])->name('bookmark_sc_post');
            Route::post('/report', [SocialConnectReportedPostsController::class, 'store'])->name('report_sc_post');

            Route::prefix('/comments')->group(function () {

                Route::get('/', [SocialConnectPostCommentsController::class, 'index'])->name('get_post_comments');
                Route::post('/', [SocialConnectPostCommentsController::class, 'store'])->name('add_post_comment');

                Route::prefix('/{comment_id}')->group(function () {

                    Route::patch('', [SocialConnectPostCommentsController::class, 'update'])->name('edit_post_comment');
                    Route::delete('', [SocialConnectPostCommentsController::class, 'destroy'])->name('delete_post_comment');

                    Route::post('/replies', [SocialConnectPostCommentRepliesController::class, 'store'])->name('add_post_comment_reply');
                    Route::patch('/replies/{reply_id}', [SocialConnectPostCommentRepliesController::class, 'update'])->name('edit_post_comment_reply');
                    Route::delete('/replies/{reply_id}', [SocialConnectPostCommentRepliesController::class, 'destroy'])->name('delete_post_comment_reply');

                });
            });
        });
    });
});

