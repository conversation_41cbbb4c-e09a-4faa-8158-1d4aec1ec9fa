<?php

use App\Http\Controllers\BookMarksController;
use App\Http\Controllers\CommentRepliesController;
use App\Http\Controllers\CommentsController;
use App\Http\Controllers\CommentVotesController;
use App\Http\Controllers\LikesController;
use App\Http\Controllers\PostsController;
use Illuminate\Support\Facades\Route;


Route::get('/', [PostsController::class, 'index'])->name('get_posts')->middleware('cache.response');

Route::prefix('/{post_id}')->group(function () {

    Route::get('/', [PostsController::class, 'show'])->name('get_post_detail');
    Route::post('/like', [LikesController::class, 'like'])->name('like_posts');
    Route::post('/bookmark', [BookMarksController::class, 'bookMark'])->name('bookmark_posts');

    Route::prefix('/comments')->group(function () {

        Route::post('/', [CommentsController::class, 'store'])->name('add_post_comment');

        Route::prefix('/{comment_id}')->group(function () {

            Route::patch('', [CommentsController::class, 'update'])->name('edit_post_comment');
            Route::delete('', [CommentsController::class, 'destroy'])->name('delete_post_comment');

            Route::post('/replies', [CommentRepliesController::class, 'store'])->name('add_post_comment_reply');
            Route::patch('/replies/{reply_id}', [CommentRepliesController::class, 'update'])->name('edit_post_comment_reply');
            Route::delete('/replies/{reply_id}', [CommentRepliesController::class, 'destroy'])->name('delete_post_comment_reply');

            Route::post('/votes', [CommentVotesController::class, 'store'])->name('add_post_comment_vote');
        });
    });
});
