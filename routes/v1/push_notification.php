<?php

use App\Http\Controllers\FcmTokenListController;
use App\Http\Controllers\UserNotificationsController;
use Illuminate\Support\Facades\Route;

Route::prefix('push-notifications')->group(function () {
    Route::get('/', [UserNotificationsController::class, 'index'])->name('get_user_notifications');
    Route::patch('{notification_id}', [UserNotificationsController::class, 'markAsRead'])->name('update_notification');
    Route::post('register-device', [UserNotificationsController::class, 'registerDevice'])->name('register_device');
    Route::post('send-notification', [UserNotificationsController::class, 'sendNotification'])->name('send_notification');
    Route::post('add_token_list', [FcmTokenListController::class, 'store'])->name('add_token_list');
});
