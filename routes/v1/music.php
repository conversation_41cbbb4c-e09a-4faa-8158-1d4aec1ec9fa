<?php

use App\Http\Controllers\BookMarksController;
use App\Http\Controllers\LikesController;
use App\Http\Controllers\MusicCommentRepliesController;
use App\Http\Controllers\MusicCommentsController;
use App\Http\Controllers\MusicController;
use Illuminate\Support\Facades\Route;


Route::get('/', [MusicController::class, 'index'])->name('get_music');
Route::get('get-by-category/{category_id}', [MusicController::class, 'getByCategory'])->name('get_music_by_category');

Route::prefix('/{music_id}')->group(function () {

    Route::get('/', [MusicController::class, 'show'])->name('get_music_detail');
    Route::post('/like', [MusicController::class, 'like'])->name('like_music');
    Route::post('/bookmark', [MusicController::class, 'bookMark'])->name('bookmark_music');

    Route::prefix('/comments')->group(function () {

        Route::post('/', [MusicCommentsController::class, 'store'])->name('add_music_comment');

        Route::prefix('/{comment_id}')->group(function () {

            Route::patch('', [MusicCommentsController::class, 'update'])->name('edit_music_comment');
            Route::delete('', [MusicCommentsController::class, 'destroy'])->name('delete_music_comment');

            Route::post('/replies', [MusicCommentRepliesController::class, 'store'])->name('add_music_comment_reply');
            Route::patch('/replies/{reply_id}', [MusicCommentRepliesController::class, 'update'])->name('edit_music_comment_reply');
            Route::delete('/replies/{reply_id}', [MusicCommentRepliesController::class, 'destroy'])->name('delete_music_comment_reply');
        });
    });
});
