<?php

use App\Http\Controllers\Admin\AdminBroadcastTopicController;
use App\Http\Controllers\Admin\AdminBroadcastMessageController;
use App\Http\Controllers\Admin\AdminCategoriesController;
use App\Http\Controllers\Admin\AdminMusicController;
use App\Http\Controllers\Admin\AdminOrganizationController;
use App\Http\Controllers\Admin\AdminPostsController;
use App\Http\Controllers\Admin\AdminReferralCodeController;
use App\Http\Controllers\Admin\AdminUserPostRequestsController;
use App\Http\Controllers\Admin\AdminUsersController;
use App\Http\Controllers\Admin\AdminVideosController;
use App\Http\Controllers\AdminsController;
use App\Http\Controllers\AffiliateCodesController;
use App\Http\Controllers\EmpowerHerTypesController;
use App\Http\Controllers\EventsController;
use App\Http\Controllers\ModuleController;
use App\Http\Controllers\PodCastsController;
use Illuminate\Support\Facades\Route;


Route::post('register', [AdminsController::class, 'register'])->name('register');
Route::post('login', [AdminsController::class, 'login'])->name('login');
Route::middleware('auth:admin')->group(function () {

    Route::get('modules', [ModuleController::class, 'index'])->name('get_modules');

    Route::get('users', [AdminUsersController::class, 'index'])->name('get_users');
    Route::get('users/{user_id}', [AdminUsersController::class, 'show'])->name('show_user');
    Route::post('users/{user_id}/change-password', [AdminUsersController::class, 'changePassword'])->name('change_user_password');
    Route::get('search-users', [AdminUsersController::class, 'search'])->name('search_user');

    Route::get('broadcast-messages', [AdminBroadcastMessageController::class, 'index'])->name('get_broadcast_messages');
    Route::post('broadcast-messages', [AdminBroadcastMessageController::class, 'store'])->name('add_broadcast_message');

    Route::get('broadcast-topics', [AdminBroadcastTopicController::class, 'index'])->name('get_broadcast_topics_list');
    Route::post('broadcast-topics', [AdminBroadcastTopicController::class, 'store'])->name('add_broadcast_topics_list');
    Route::get('broadcast-topics/{id}', [AdminBroadcastTopicController::class, 'show'])->name('show_broadcast_topics_list');
    Route::post('broadcast-topics/{id}/sync-users', [AdminBroadcastTopicController::class, 'sync'])->name('sync_users_to_broadcast_topics_list');

    Route::get('organizations', [AdminOrganizationController::class, 'index'])->name('get_organizations');
    Route::get('referral-codes', [AdminReferralCodeController::class, 'index'])->name('get_referral_codes');

    Route::get('categories', [AdminCategoriesController::class, 'index'])->name('get_categories');
    Route::post('categories', [AdminCategoriesController::class, 'store'])->name('add_categories');
    Route::put('categories/{category_id}', [AdminCategoriesController::class, 'update'])->name('update_categories');
    //Route::resource('categories', AdminCategoriesController::class)->only(['index',  'store', 'update']);
    Route::resource('affiliate-codes', AffiliateCodesController::class);

    Route::patch('user-post-requests/{user_post_request_id}', [AdminUserPostRequestsController::class, 'updateStatus'])->name('update_user_post_request_status');
    Route::get('user-post-requests', [AdminUserPostRequestsController::class, 'index'])->name('get_user_post_requests');

    Route::post('modules/{module_id}/posts', [AdminPostsController::class, 'store'])->name('add_posts');
    Route::put('modules/{module_id}/posts/{post_id}', [AdminPostsController::class, 'update'])->name('edit_posts');
    Route::delete('modules/{module_id}/posts/{post_id}', [AdminPostsController::class, 'destroy'])->name('delete_posts');
    Route::get('modules/{module_id}/posts', [AdminPostsController::class, 'index'])->name('get_posts');
    Route::get('modules/{module_id}/posts/{post_id}', [AdminPostsController::class, 'show'])->name('get_post_detail_for_admin');

    Route::get('modules/{module_id}/music', [AdminMusicController::class, 'index'])->name('get_music');
    Route::post('modules/{module_id}/music', [AdminMusicController::class, 'store'])->name('add_music');
    Route::put('modules/{module_id}/music/{music_id}', [AdminMusicController::class, 'update'])->name('edit_music');
    Route::delete('modules/{module_id}/music/{music_id}', [AdminMusicController::class, 'destroy'])->name('delete_music');
    Route::get('modules/{module_id}/music/{music_id}', [AdminMusicController::class, 'show'])->name('get_music_detail_for_admin');


    Route::get('modules/{module_id}/empower-her', [EmpowerHerTypesController::class, 'index'])->name('get_empower_here_types');
    Route::post('modules/{module_id}/empower-her', [EmpowerHerTypesController::class, 'store'])->name('add_empower_here_types');

    Route::get('modules/{module_id}/events', [EventsController::class, 'index'])->name('get_events');
    Route::get('modules/{module_id}/events/locations/{location_id}', [EventsController::class, 'showLocation'])->name('get_event_location_detail');
    Route::post('modules/{module_id}/events', [EventsController::class, 'store'])->name('add_event');

    Route::get('modules/{module_id}/podcasts', [PodCastsController::class, 'index'])->name('get_podcasts');
    Route::post('modules/{module_id}/podcasts', [PodCastsController::class, 'store'])->name('add_podcast');

    Route::get('modules/{module_id}/videos', [AdminVideosController::class, 'index'])->name('get_video');
    Route::get('modules/{module_id}/videos/{video_id}', [AdminVideosController::class, 'show'])->name('get_video_detail');
    Route::put('modules/{module_id}/videos/{video_id}', [AdminVideosController::class, 'update'])->name('edit_video');
    Route::delete('modules/{module_id}/videos/{video_id}', [AdminVideosController::class, 'destroy'])->name('delete_video');
    Route::post('modules/{module_id}/videos', [AdminVideosController::class, 'store'])->name('add_video');
    Route::get('modules/{module_id}/videos/{video_id}', [AdminVideosController::class, 'show'])->name('get_video_detail_for_admin');

});
