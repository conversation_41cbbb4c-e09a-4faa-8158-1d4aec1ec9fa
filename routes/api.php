<?php

use App\Http\Controllers\EmpowerHerPostsController;
use App\Http\Controllers\EmpowerHerTypesController;
use App\Http\Controllers\EventsController;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\ModuleController;
use App\Http\Controllers\NewsCommentRepliesController;
use App\Http\Controllers\NewsCommentsController;
use App\Http\Controllers\NewsController;
use App\Http\Controllers\NewsResourcesController;

use App\Http\Controllers\ResetPasswordController;
use App\Http\Controllers\UserIOSBiometricController;
use App\Http\Controllers\VideoCommentRepliesController;
use App\Http\Controllers\VideoCommentsController;
use App\Http\Controllers\VideosController;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Redis;

Route::prefix('v1')->group(function () {

    Route::middleware('cache.response')->get('/test-cache1', function () {
        logger('hit route');
        return response()->json(['ok' => true]);
    });
    Route::get('debug/redis-db', function () {
        $redis = Redis::connection();
        $db = $redis->command('config', ['get', 'databases']);
        $keys = $redis->keys('*');

        return response()->json([
            'all_keys' => $keys,
            'redis_config' => $db,
        ]);
    });

    Route::middleware('auth:user')->group(function () {

        Route::post('reset-password', [ResetPasswordController::class, 'resetPassword'])->name('reset_password');

        Route::get('home', [HomeController::class, 'index'])->name('home_api')->middleware('cache.response');
        Route::get('modules', [ModuleController::class, 'index'])->name('get_modules')->middleware('cache.response');
        Route::get('modules/like-able', [ModuleController::class, 'getLikeAble'])->name('get_likeable_modules')->middleware('cache.response');
        Route::get('modules/bookmark-able', [ModuleController::class, 'getBookMarkAble'])->name('get_bookmarkable_modules')->middleware('cache.response');

        Route::post('users/{user_id}/register-ios-biometric', [UserIOSBiometricController::class, 'store'])->name('register_ios_biometric');

        Route::get('modules/{module_id}/empower-her', [EmpowerHerTypesController::class, 'index'])->name('get_empower_here_types');
        Route::get('modules/{module_id}/empower-her/{type_id}', [EmpowerHerPostsController::class, 'index'])->name('get_empower_here_types_detail');
        Route::post('modules/{module_id}/empower-her/{type_id}', [EmpowerHerPostsController::class, 'store'])->name('like_empower_her_post');
        Route::get('modules/{module_id}/empower-her/{type_id}/posts/{post_id}', [EmpowerHerPostsController::class, 'show'])->name('get_empower_here_post_detail');

        Route::get('modules/{module_id}/events', [EventsController::class, 'index'])->name('get_events');
        Route::get('modules/{module_id}/events/locations/{location_id}', [EventsController::class, 'showLocation'])->name('get_event_location_detail');
        Route::get('modules/{module_id}/events/{event_id}', [EventsController::class, 'show'])->name('get_event_detail');

        Route::get('modules/{module_id}/videos', [VideosController::class, 'index'])->name('get_video');
        Route::get('modules/{module_id}/videos-by-category/{category_id}', [VideosController::class, 'getByCategory'])->name('get_videos_by_category');
        Route::get('modules/{module_id}/videos/{video_id}', [VideosController::class, 'show'])->name('get_video_detail');
        Route::post('modules/{module_id}/videos/{video_id}/like', [VideosController::class, 'like'])->name('like_video');
        Route::post('modules/{module_id}/videos/{video_id}/bookmark', [VideosController::class, 'bookmark'])->name('bookmark_video');

        Route::post('modules/{module_id}/videos/{video_id}/comments', [VideoCommentsController::class, 'store'])
            ->name('add_video_comment');
        Route::patch('modules/{module_id}/videos/{video_id}/comments/{comment_id}', [VideoCommentsController::class, 'update'])
            ->name('edit_video_comment');
        Route::delete('modules/{module_id}/videos/{video_id}/comments/{comment_id}', [VideoCommentsController::class, 'destroy'])
            ->name('delete_video_comment');

        Route::post('modules/{module_id}/videos/{video_id}/comments/{comment_id}/replies', [VideoCommentRepliesController::class, 'store'])
            ->name('add_video_comment_reply');
        Route::patch('modules/{module_id}/videos/{video_id}/comments/{comment_id}/replies/{reply_id}', [VideoCommentRepliesController::class, 'update'])
            ->name('edit_video_comment_reply');
        Route::delete('modules/{module_id}/videos/{video_id}/comments/{comment_id}/replies/{reply_id}', [VideoCommentRepliesController::class, 'destroy'])
            ->name('delete_video_comment_reply');

        Route::get('modules/{module_id}/news-sources', [NewsResourcesController::class, 'index'])->name('get_news_sources');
        Route::post('news-sources', [NewsResourcesController::class, 'store'])->name('post_news_sources');
        Route::get('modules/{module_id}/news-sources/{source_id}/news', [NewsController::class, 'index'])->name('get_news_by_source');
        Route::get('modules/{module_id}/news/{news_id}', [NewsController::class, 'show'])->name('get_news_detail');
        Route::post('news', [NewsController::class, 'store'])->name('post_news');

        Route::post('modules/{module_id}/news/{news_id}/like', [NewsController::class, 'like'])
            ->name('like_news');

        Route::post('modules/{module_id}/news/{news_id}/bookmark', [NewsController::class, 'bookmark'])
            ->name('bookmark_news');

        Route::post('modules/{module_id}/news/{news_id}/comments', [NewsCommentsController::class, 'store'])
            ->name('add_news_comment');
        Route::patch('modules/{module_id}/news/{news_id}/comments/{comment_id}', [NewsCommentsController::class, 'update'])
            ->name('edit_news_comment');
        Route::delete('modules/{module_id}/news/{news_id}/comments/{comment_id}', [NewsCommentsController::class, 'destroy'])
            ->name('delete_news_comment');

        Route::post('modules/{module_id}/news/{news_id}/comments/{comment_id}/replies', [NewsCommentRepliesController::class, 'store'])
            ->name('add_news_comment_reply');
        Route::patch('modules/{module_id}/news/{news_id}/comments/{comment_id}/replies/{reply_id}', [NewsCommentRepliesController::class, 'update'])
            ->name('edit_news_comment_reply');
        Route::delete('modules/{module_id}/news/{news_id}/comments/{comment_id}/replies/{reply_id}', [NewsCommentRepliesController::class, 'destroy'])
            ->name('delete_news_comment_reply');


    });
});
