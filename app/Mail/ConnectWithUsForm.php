<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use MailerSend\LaravelDriver\MailerSendTrait;

class ConnectWithUsForm extends Mailable
{
    use Queueable, SerializesModels, MailerSendTrait;


    public $userEmail, $firstName, $lastName, $phoneNumber, $topic, $userMessage;

    /**
     * Create a new message instance.
     */
    public function __construct($userEmail, $firstName, $lastName, $phoneNumber, $topic, $userMessage)
    {
        $this->userEmail = $userEmail;
        $this->firstName = $firstName;
        $this->lastName = $lastName;
        $this->phoneNumber = $phoneNumber;
        $this->topic = $topic;

        $this->userMessage = $userMessage;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'User query | Connect with us form',
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'email.connectWithUsForm',
            with: [
                'userEmail' => $this->userEmail,
                'firstName' => $this->firstName,
                'lastName' => $this->lastName,
                'phoneNumber' => $this->phoneNumber,
                'topic' => $this->topic,
                'userMessage' => $this->userMessage,
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
