<?php

namespace App\Listeners;

use App\Events\RegisteringUser;
use App\Mail\UserRegistration;
use App\Services\OTPService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class RegisteringUserListener
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(RegisteringUser $event): void
    {
        $otpService = new OTPService();
        $otp = $otpService->getOTP($event->user);

        Mail::to($event->user)->send(new UserRegistration($event->user, $otp));
    }
}
