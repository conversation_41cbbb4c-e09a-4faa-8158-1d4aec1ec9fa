<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AIMessage extends Model
{
    protected $table = 'ai_messages';
    protected $connection = 'akina_ai';

    protected $fillable = ['conversation_id', 'user_id', 'sender_type', 'content', 'external_message_id'];

    public function conversation(): BelongsTo
    {
        return $this->belongsTo(AIConversation::class);
    }
}
