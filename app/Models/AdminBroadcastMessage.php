<?php

namespace App\Models;

use App\Enums\BroadcastMessageType;
use Illuminate\Database\Eloquent\Model;
use <PERSON><PERSON>Idea\Helper\App\Models\_IH_User_C;

class AdminBroadcastMessage extends Model
{
    protected $fillable = [
        'type', 'type_value', 'title', 'body', 'url', 'admin_id',
    ];

    protected $casts = [
        'type' => BroadcastMessageType::class,
    ];

    protected $appends = ['targeted_users'];

    public function getTypeValueAttribute($value)
    {
        if ($this->type === BroadcastMessageType::USER) {

            return is_string($this->type) ? json_decode($value, true) : $value;
        }

        return $value;
    }

    public function admin()
    {
        return $this->belongsTo(Admin::class);
    }

    public function getTargetedUsersAttribute(): array|_IH_User_C|\Illuminate\Support\Collection
    {
        if ($this->type === BroadcastMessageType::USER) {
            return User::whereIn('id', json_decode($this->type_value, true))->get();
        }

        return collect();
    }
}
