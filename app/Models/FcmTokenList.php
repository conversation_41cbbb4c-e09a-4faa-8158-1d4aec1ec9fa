<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class FcmTokenList extends Model
{
    protected $fillable = ['name'];

    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'fcm_token_list_users')->distinct();
    }

    public function tokens(): BelongsToMany
    {
        return $this->belongsToMany(FCMToken::class, 'fcm_token_list_users','fcm_token_list_id', 'fcm_token_id')
            ->withPivot(['user_id'])
            ->withTimestamps();
    }

}
