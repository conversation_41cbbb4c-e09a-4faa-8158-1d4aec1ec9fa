<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;

class Admin extends Authenticatable
{
    use <PERSON><PERSON><PERSON>Tokens, HasFactory, Notifiable;

    protected $hidden = [
        'password',
        'email_verified_at',
        'created_at',
        'updated_at',
    ];
}
