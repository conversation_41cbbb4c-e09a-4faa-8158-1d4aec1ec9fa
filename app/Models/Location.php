<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Location extends Model
{
    use HasFactory;

    protected $fillable = ['name','module_id','thumbnail','sub_title'];

    /**
     * Get the events for the location.
     */
    public function events(): HasMany
    {
        return $this->hasMany(Event::class);
    }
}
