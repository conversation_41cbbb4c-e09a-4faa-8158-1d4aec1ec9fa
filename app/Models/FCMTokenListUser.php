<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;

class FCMTokenListUser extends Model
{
    protected $fillable = ['fcm_token_list_id', 'user_id', 'fcm_token_id'];
    protected $table = 'fcm_token_list_users';

    public function tokens()
    {
        return $this->belongsTo(FCMToken::class, 'fcm_token_id');
    }
}
