<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AIConversation extends Model
{
    protected $table = 'ai_conversations';
    protected $connection = 'akina_ai';

    protected $fillable = ['user_id', 'external_conversation_id', 'title'];

    public function messages(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(AIMessage::class, 'conversation_id')->orderBy('id', 'asc');
    }
}
