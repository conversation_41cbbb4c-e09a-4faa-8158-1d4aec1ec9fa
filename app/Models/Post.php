<?php

namespace App\Models;

use App\Models\Scopes\PostPublishedScope;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;

class Post extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'content',
        'module_id',
        'thumbnail',
        'status'
    ];

    protected static function booted(): void
    {
        static::addGlobalScope(new PostPublishedScope);
    }

    public function likes(): MorphMany
    {
        return $this->morphMany(Like::class, 'likeable');
    }

    public function bookmarks(): MorphMany
    {
        return $this->morphMany(BookMark::class, 'bookmarkable');
    }

    public function comments(): MorphMany
    {
        return $this->morphMany(Comment::class, 'commentable')->orderByDesc('created_at');
    }

    public function categories(): BelongsToMany
    {
        return $this->belongsToMany(Category::class,'post_categories')->withTimestamps();
    }
}
