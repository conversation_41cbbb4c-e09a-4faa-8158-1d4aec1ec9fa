<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserFollower extends Model
{
    use HasFactory;

    public function userFollower(): BelongsTo
    {
        return $this->belongsTo(User::class,'user_id', 'id');
    }
    public function userFollowing(): BelongsTo
    {
        return $this->belongsTo(User::class,'follower_id', 'id');
    }
}
