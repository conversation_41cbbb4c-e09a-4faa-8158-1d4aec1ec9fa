<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserNotificationPreference extends Model
{
    use HasFactory;

    protected $fillable = [
        'general_information',
        'sound',
        'vibrate',
        'app_updates',
        'bill_reminder',
        'promotion',
        'discount_available',
        'payment_request',
        'new_service_available',
        'new_tips_available',
        'user_id',
    ];

    protected $casts = [
        'general_information' => 'boolean',
        'sound' => 'boolean',
        'vibrate' => 'boolean',
        'app_updates' => 'boolean',
        'bill_reminder' => 'boolean',
        'promotion' => 'boolean',
        'discount_available' => 'boolean',
        'payment_request' => 'boolean',
        'new_service_available' => 'boolean',
        'new_tips_available' => 'boolean',
    ];
}
