<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class StripePaymentLog extends Model
{
    protected $casts = [
        'metadata' => 'array',
        'payload' => 'array',
    ];

    protected $fillable = [
        'user_id',
        'action_type',
        'customer_id',
        'subscription_id',
        'payment_intent_id',
        'amount',
        'currency',
        'status',
        'metadata',
        'payload',
    ];
}
