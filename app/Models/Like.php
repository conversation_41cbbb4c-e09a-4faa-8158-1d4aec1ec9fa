<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class Like extends Model
{
    use HasFactory;

    protected $fillable = ['module_id','user_id'];

    public function likeable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Get the user's most recent image.
     */
    public function latestImage(): MorphOne
    {
        return $this->morphOne(Post::class, 'likeable')->latestOfMany();
    }

}
