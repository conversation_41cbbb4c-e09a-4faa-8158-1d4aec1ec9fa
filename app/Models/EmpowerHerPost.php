<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class EmpowerHerPost extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'subtitle',
        'content',
        'module_id',
        'thumbnail',
        'button_link',
        'button_text',
        'empower_her_type_id'
    ];

    public function images(): HasMany
    {
        return $this->hasMany(EmpowerHerPostImage::class);
    }
}
