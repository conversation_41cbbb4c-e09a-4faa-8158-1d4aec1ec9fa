<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class FCMToken extends Model
{
    use HasFactory, softDeletes;

    protected $table = 'fcm_tokens';

    protected $fillable = [
        'user_id',
        'device_token',
        'device_type',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
