<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class StripeWebhookLog extends Model
{

    protected $fillable = [
        'event_id',
        'type',
        'customer_id',
        'payload',
        'status',
        'error',
        'processing_notes'
    ];

    protected $casts = [
        'payload' => 'array',
    ];
}
