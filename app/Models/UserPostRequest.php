<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasOne;

class UserPostRequest extends Model
{

    protected $fillable = [
        'user_id',
        'post_id',
        'status'
    ];

    public function user(): HasOne
    {
        return $this->hasOne(User::class,'id','user_id');
    }

    public function post(): HasOne
    {
        return $this->hasOne(Post::class,'id','post_id');
    }
}
