<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserNotification extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'title',
        'body',
        'data',
        'is_sent',
        'admin_broadcast_message_id',
        'batch_id',
    ];

    protected $casts = [
        'data' => 'array',
        'is_sent' => 'bool',
        'is_read' => 'bool',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
