<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Lara<PERSON>\Cashier\Billable;
use Laravel\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    use Billable, HasApiTokens, HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'first_name',
        'last_name',
        'user_name',
        'email',
        'affiliate_code',
        'password',
        'profile_photo',
        'cover_photo',
        'user_type',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
        'otp',
        'referral_code',
        'otps',
        'email_verified_at',
        'pm_type',
        'pm_last_four',
        'organization_id',
        'trial_ends_at',
        'created_at',
        'updated_at',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'is_student'=> 'boolean',
        ];
    }

    public function getFullNameAttribute(): string
    {
        return "{$this->first_name} {$this->last_name}";
    }

    public function otps(): HasMany
    {
        return $this->hasMany(OTP::class);
    }

    public function profile(): HasOne
    {
        return $this->hasOne(UserProfile::class);
    }

    public function notificationPreferences(): HasOne
    {
        return $this->hasOne(UserNotificationPreference::class);
    }

    public function iosKey(): HasOne
    {
        return $this->hasOne(UserIOSBiometricKey::class);
    }

    public function comments(): HasMany
    {
        return $this->hasMany(Comment::class)->orderByDesc('created_at');
    }

    public function followers(): HasMany
    {
        return $this->hasMany(UserFollower::class, 'user_id', 'id')->orderByDesc('created_at');
    }

    public function followings(): HasMany
    {
        return $this->hasMany(UserFollower::class, 'follower_id', 'id')->orderByDesc('created_at');
    }

    public function likes(): HasMany
    {
        return $this->hasMany(Like::class);
    }

    public function socialConnectLikes(): HasMany
    {
        return $this->hasMany(Like::class)->where('likeable_type', SocialConnectPost::class);
    }

    public function notifications(): HasMany
    {
        return $this->hasMany(UserNotification::class);
    }

    public function fcmTokens(): HasMany
    {
        return $this->hasMany(FCMToken::class);
    }

    public function blockedUsers(): HasMany
    {
        return $this->hasMany(BlockedUser::class);
    }

    public function organization(): HasOne
    {
        return $this->hasOne(Organization::class, 'id', 'organization_id');
    }

    public function fcmTokenLists(): BelongsToMany
    {
        return $this->belongsToMany(FcmTokenList::class, 'fcm_token_list_users');
    }

}
