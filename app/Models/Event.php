<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Event extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'sub_title',
        'content',
        'module_id',
        'thumbnail',
        'button_link',
        'button_text',
        'event_date',
        'is_akina_event',
        'location_id'
    ];

    /**
     * Get the category that owns the music.
     */
    public function location(): BelongsTo
    {
        return $this->belongsTo(Location::class);
    }
}
