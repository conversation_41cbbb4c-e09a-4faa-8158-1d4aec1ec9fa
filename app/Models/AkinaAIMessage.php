<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AkinaAIMessage extends Model
{
    use HasFactory;

    protected $table = 'akina_ai_messages';

    protected $fillable = ['conversation_id', 'user_id', 'sender_type', 'content', 'external_message_id'];

    public function conversation(): BelongsTo
    {
        return $this->belongsTo(AkinaAIConversation::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
