<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Category extends Model
{
    use HasFactory;

    protected $fillable = ['name', 'module_id', 'layout', 'description', 'slug'];

    /**
     * Get the music for the category.
     */
    public function music(): HasMany
    {
        return $this->hasMany(Music::class);
    }

    /**
     * Get the videos for the category.
     */
    public function videos(): Has<PERSON><PERSON>
    {
        return $this->hasMany(Video::class);
    }

    /**
     * Get the podcasts for the category.
     */
    public function podcasts(): Has<PERSON><PERSON>
    {
        return $this->hasMany(PodCast::class);
    }

    /**
     * Get the posts for the category.
     */
    public function posts(): BelongsToMany
    {
        return $this->belongsToMany(Post::class, 'post_categories');
    }
}
