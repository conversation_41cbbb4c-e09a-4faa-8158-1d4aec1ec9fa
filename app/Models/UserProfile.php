<?php

namespace App\Models;

use App\Traits\Encryptable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserProfile extends Model
{
    use HasFactory;

    use Encryptable;

    protected $encryptable = [
        'education_level',
        'income_level',
        'marital_status',
        'children_option',
        'interests_option',
        'joining_motivation_options',
        'address',
        'date_of_birth',
        'phone_number',
        'nick_name',
        'bio',
    ];

    protected $fillable = [
        'education_level',
        'income_level',
        'marital_status',
        'children_option',
        'interests_option',
        'joining_motivation_options',
        'user_id',
        'address',
        'date_of_birth',
        'phone_number',
        'nick_name',
        'bio',
        'is_terms_accepted',
        'is_completed',
    ];

    protected $casts = [
        'is_terms_accepted' => 'boolean',
        'is_completed' => 'boolean',
    ];
}
