<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Tag extends Model
{
    use HasFactory;

    /**
     * Get the music for the tag.
     */
    public function music(): Has<PERSON><PERSON>
    {
        return $this->hasMany(Music::class);
    }

    /**
     * Get the videos for the tag.
     */
    public function videos(): Has<PERSON><PERSON>
    {
        return $this->hasMany(Video::class);
    }

    /**
     * Get the podcasts for the tag.
     */
    public function podcasts(): Has<PERSON><PERSON>
    {
        return $this->hasMany(PodCast::class);
    }
}
