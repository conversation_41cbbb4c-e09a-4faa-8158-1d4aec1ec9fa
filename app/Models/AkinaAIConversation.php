<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AkinaAIConversation extends Model
{
    use HasFactory;

    protected $table = 'akina_ai_conversations';

    protected $fillable = ['user_id', 'external_conversation_id', 'title'];

    public function messages(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(AkinaAIMessage::class, 'conversation_id')->orderBy('id', 'asc');
    }

    public function user(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
