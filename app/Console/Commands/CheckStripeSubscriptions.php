<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use Stripe\Stripe;
use Stripe\Subscription;
use Illuminate\Support\Facades\Log;

class CheckStripeSubscriptions extends Command
{
    protected $signature = 'subscriptions:check-premium-status';
    protected $description = 'Check active Stripe subscriptions against premium status in DB';

    public function handle()
    {
        Stripe::setApiKey(config('cashier.secret'));

        $users = User::whereNotNull('stripe_id')->get();

        foreach ($users as $user) {
            try {
                $subscriptions = Subscription::all([
                    'customer' => $user->stripe_id,
                    'status' => 'all', // we'll filter manually
                    'limit' => 10,
                ]);

                $hasActive = collect($subscriptions->data)->contains(function ($subscription) {
                    return in_array($subscription->status, ['active', 'trialing']);
                });

                if ($hasActive && $user->user_type !== 'premium') {
                    Log::warning("User ID {$user->id} has an active Stripe subscription but is not marked premium in DB.");

                    // Optionally fix it automatically:
                    // $user->update(['type' => 'premium']);
                }

            } catch (\Exception $e) {
                Log::error("Failed to fetch subscriptions for user {$user->id}: " . $e->getMessage());
            }
        }

        $this->info("Subscription status check completed.");
    }
}
