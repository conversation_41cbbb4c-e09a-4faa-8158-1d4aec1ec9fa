<?php

namespace App\Console\Commands;

use App\Jobs\ImportNewsJob;
use Illuminate\Console\Command;

class ImportNewsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'news:import-news {--import-date-after=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fetch News and import into DB';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $importAfterDate = $this->option('import-date-after');

        ImportNewsJob::dispatch($importAfterDate);
    }
}
