<?php

namespace App\Console\Commands;

use App\Jobs\CleanOldApiLimits;
use Illuminate\Console\Command;

class DispatchApiLimitCleanup extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'api-limit:dispatch-api-limit-cleanup';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "del entries from the api_request_limits table";

    /**
     * Execute the console command.
     */
    public function handle()
    {
        CleanOldApiLimits::dispatch();
    }
}
