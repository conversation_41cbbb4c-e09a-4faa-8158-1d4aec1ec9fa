<?php

namespace App\Console\Commands;

use App\Jobs\ImportPodcastsJob;
use Illuminate\Console\Command;
use Illuminate\Queue\Jobs\Job;

class ImportPodcastsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'podcasts:import-podcasts';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fetch Podcasts and import into DB';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        ImportPodcastsJob::dispatch();
    }
}
