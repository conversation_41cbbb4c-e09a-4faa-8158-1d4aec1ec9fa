<?php

namespace App\Console\Commands;

use App\Enums\UserMembershipType;
use Illuminate\Console\Command;
use App\Models\User;
use Stripe\Stripe;
use Stripe\Subscription;
use Illuminate\Support\Facades\Log;

class CheckPremiumWithoutActiveStripe extends Command
{
    protected $signature = 'subscriptions:check-fake-premium';
    protected $description = 'Find users marked as premium in DB but without any active Stripe subscription';

    public function handle()
    {
        Stripe::setApiKey(config('cashier.secret'));

        // Assuming 'type' is used to check for premium users
        $users = User::where('user_membership_type', UserMembershipType::STRIPE_SUBSCRIPTION->value)
            ->whereNotNull('stripe_id')
            ->get();

        foreach ($users as $user) {
            try {
                $subscriptions = Subscription::all([
                    'customer' => $user->stripe_id,
                    'status' => 'all', // We will filter manually
                    'limit' => 10,
                ]);

                $hasActive = collect($subscriptions->data)->contains(function ($subscription) {
                    return in_array($subscription->status, ['active', 'trialing']);
                });

                if (!$hasActive) {
                    Log::warning("User ID {$user->id} is marked premium but has no active Stripe subscription.");

                    // Optionally downgrade:
                    // $user->update(['type' => 'free']);
                }

            } catch (\Exception $e) {
                Log::error("Stripe error for user ID {$user->id}: " . $e->getMessage());
            }
        }

        $this->info("Check completed: Premium users without active subscriptions.");
    }
}
