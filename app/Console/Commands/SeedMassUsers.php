<?php

namespace App\Console\Commands;

use App\Models\SocialConnectPost;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Symfony\Component\Console\Command\Command as CommandAlias;
use Faker\Factory as Faker;


class SeedMassUsers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:seed-mass-users';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Insert 1 million users and each user has 2 posts';

    /**
     * Execute the console command.
     * @throws \Throwable
     */
    public function handle()
    {
        $faker = Faker::create();
        $totalUsers = 500000; // Change to 1_000_000 for full scale
        $batchSize = 5000;

        $password = bcrypt('password');
        $lastUserId = DB::table('users')->max('id') ?? 0;

        for ($i = 0; $i < $totalUsers; $i += $batchSize) {
            $users = [];
            $posts = [];

            for ($j = 0; $j < $batchSize; $j++) {
                $index = $i + $j;
                $userId = $lastUserId + $index + 1;

                $users[] = [
                    'id' => $userId, // pre-assigning ID
                    'first_name' => $faker->firstName,
                    'last_name' => $faker->lastName,
                    'email' => "akina-user{$index}@akina.com",
                    'user_name' => "akina-user{$index}",
                    'password' => $password,
                    'created_at' => now(),
                    'updated_at' => now(),
                ];

                // 2 posts per user
                for ($k = 0; $k < 2; $k++) {
                    $postData = [
                        'module_id' => 13,
                        'user_id' => $userId,
                        'text' => $faker->sentence(6),
                        'type' => 'text',
                        'media_link' => null,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ];
                    if ($k === 1) {
                        $postData['type'] = 'post';
                        $postData['media_link'] = 'https://akina-app-v2.s3.amazonaws.com/sc_photos/1734102462_n-text-sc-photo.png';
                    }
                    $posts[] = $postData;
                }
            }

            // Bulk insert users and posts
            DB::table('users')->insert($users);
           // DB::table('social_connect_posts')->insert($posts);
            foreach (array_chunk($posts, 2000) as $chunk) {
                DB::table('social_connect_posts')->insert($chunk);
            }

            $this->info("Inserted users {$i} to " . ($i + $batchSize));
        }

        $this->info("✅ Seeding complete: {$totalUsers} users, " . ($totalUsers * 2) . " posts");
    }
}
