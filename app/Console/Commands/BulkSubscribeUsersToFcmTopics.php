<?php

namespace App\Console\Commands;

use App\Enums\UserType;
use App\Models\FCMToken;
use App\Models\FCMTokenListUser;
use App\Models\User;
use App\Services\PushNotificationService;
use Illuminate\Console\Command;

class BulkSubscribeUsersToFcmTopics extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:bulk-subscribe-users-to-fcm-topics';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Bulk subscribe all users to predefined FCM topics';

    /**
     * Execute the console command.
     */
    public function handle(PushNotificationService $pushNotificationService)
    {
        $messaging = $pushNotificationService->getMessaging();

        $this->info('Starting FCM topic subscriptions...');

        $this->truncateData();

        // Free Users
        $this->subscribeUsersToTopic(UserType::FREE->value, config('firebase.free_user_topic'), $pushNotificationService);

        // Premium Users
        $this->subscribeUsersToTopic(UserType::PREMIUM->value, config('firebase.premium_user_topic'), $pushNotificationService);

        // All Users
        $this->subscribeUsersToTopic('all', config('firebase.default_topic'), $pushNotificationService);

        $this->info('All subscriptions completed.');
    }

    private function truncateData()
    {
        $this->info('Truncating FCM token list users...');

        // Truncate the FCM token list users table
        FCMTokenListUser::truncate();

        $this->info('FCM token list users truncated successfully.');
    }

    private function subscribeUsersToTopic(string|null $userType, string $topic, PushNotificationService $pushNotificationService): void
    {
        $this->info("Subscribing '$userType' users to topic '$topic'...");

        User::when($userType, function ($query) use ($userType) {
            return $userType == 'all' ? $query->whereNotNull('user_type') : $query->where('user_type', $userType);
        })
            ->with(['fcmTokens' => fn($q) => $q->whereNull('deleted_at')])
            ->chunk(100, function ($users) use ($topic, $pushNotificationService) {
                foreach ($users as $user) {
                    $pushNotificationService->subscribeToTopic($user, $topic);
                }
            });
    }
}
