<?php

namespace App\Jobs;

use App\Models\Module;
use App\Models\News;
use App\Models\NewsSource;
use App\Services\FetchNewsService;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class ImportNewsJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private $importAfterDate = false;

    /**
     * Create a new job instance.
     */
    public function __construct($importAfterDate)
    {
        $this->importAfterDate = $importAfterDate ?? Carbon::now()->subDay()->toDateString();
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $module = Module::where('type', 'news')->firstOrFail();
        $sourceArr = NewsSource::where('module_id', $module->id)->get();
        $fetchNewsService = new FetchNewsService();

        foreach ($sourceArr as $source) {
            for ($page = 1; $page < 50; $page++) {
                $newsArr = $fetchNewsService->getNewsFromSource($source->external_id, 100, $page, $this->importAfterDate);
                if ($newsArr['data']) {
                    $data = [];
                    $i = 0;
                    foreach ($newsArr['data'] as $news) {
                        $newsDate = Carbon::createFromTimestamp($news['timestamp'])->toDate();
                        $data[$i]['external_id'] = $news['id'];
                        $data[$i]['module_id'] = $module->id;
                        $data[$i]['news_source_id'] = $source->external_id;
                        $data[$i]['title'] = $news['headline'];
                        $data[$i]['description'] = $news['summary_text'];
                        $data[$i]['author'] = $news['author'];
                        $data[$i]['news_date'] = $newsDate;
                        $data[$i]['url'] = $news['article_uri'];
                        $data[$i]['thumbnail'] = $news['news_image'];
                        $i++;
                    }

                    News::upsert($data, ['external_id'], ['title']);
                    if (count($newsArr['data']) < 100) {
                        break;
                    }
                }
            }
        }
    }
}
