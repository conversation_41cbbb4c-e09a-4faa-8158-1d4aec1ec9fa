<?php

namespace App\Jobs;

use App\Services\PushNotificationService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class SendNotificationToTopicJob implements ShouldQueue
{
    use Queueable, Dispatchable, InteractsWithQueue, SerializesModels;

    private $topic;
    private $title;
    private $body;
    private $data;

    /**
     * Create a new job instance.
     */
    public function __construct($topic, $title, $body, $data = [], private $broadCastMessageId = null)
    {
        $this->topic = $topic;
        $this->title = $title;
        $this->body = $body;
        $this->data = $data;
    }

    /**
     * Execute the job.
     */
    public function handle(PushNotificationService $pushNotificationService): void
    {
        $pushNotificationService->sendNotificationToTopic(
            $this->topic,
            $this->title,
            $this->body,
            $this->data,
            $this->broadCastMessageId
        );
    }
}
