<?php

namespace App\Jobs;

use App\Models\Category;
use App\Models\Module;
use App\Models\PodCast;
use App\Models\PodCastLink;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class ImportPodcastsJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $module = Module::where('type', 'podcast')->firstOrFail();
        $urlArr = PodCastLink::where('is_active', true)->get();
        $podcastsArray = [];
        $guidArr = [];
        foreach ($urlArr as $url) {
            $url = $url->link;
            $feedXML = simplexml_load_file($url);

            if (empty($feedXML)) {
                $ch = curl_init($url);

                curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 6.2; WOW64; rv:17.0) Gecko/20100101 Firefox/17.0');
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
                curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);

                $result = curl_exec($ch);

                if (substr($result, 0, 5) == "<?xml") {
                    $feedXML = simplexml_load_string($result);
                }

                curl_close($ch);
            }

            $skipped_count = 0;
            //$import_date_from = !is_numeric( $import_date_from ) ? strtotime( $import_date_from ) : intval( $import_date_from );
            $import_date_from = false;


            if ($feedXML->channel->item) {
                $feed_item_itunes1 = $feedXML->channel->children('itunes', true);

                $categoryArray = [];
                $categoryArray['name'] = $feed_item_itunes1->category->attributes() ? (string)$feed_item_itunes1->category->attributes()->text : 'uncategorized';
                $categoryArray['module_id'] = $module->id;
                $categoryArray['layout'] = 'sq-slider';

                $category = Category::firstOrCreate($categoryArray);

                for ($i = 0; $i < count($feedXML->channel->item); $i++) {

                    if( $import_date_from !== false ) {
                        if( strtotime( (string) $feedXML->channel->item[ $i ]->pubDate ) < $import_date_from ) {
                            $skipped_count++;
                            continue;
                        }
                    }
                    $pub_date = Carbon::createFromTimeString($feedXML->channel->item[ $i ]->pubDate);

                    $feed_item = $feedXML->channel->item[$i];
                    $feed_item_itunes = $feed_item->children('itunes', true);

                    $guidArr[$i]['guid'] = (string)$feedXML->channel->item[$i]->guid;
                    $podcastsArray[$i]['guid'] = (string)$feedXML->channel->item[$i]->guid;
                    $podcastsArray[$i]['title'] = (string)$feedXML->channel->item[$i]->title;
                    $podcastsArray[$i]['description'] = (string)$feedXML->channel->item[$i]->description;
                    $podcastsArray[$i]['audio_link'] = (string)$feedXML->channel->item[$i]->enclosure['url'];
                    $podcastsArray[$i]['thumbnail'] = $feed_item_itunes->image->attributes() ? (string)$feed_item_itunes->image->attributes()->href : '';
                    $podcastsArray[$i]['category_id'] = $category->id;
                    $podcastsArray[$i]['pub_date'] = $pub_date->toDateTimeString();

                }
            }
            PodCast::upsert($podcastsArray,['guid'],['title']);
        }
    }
}
