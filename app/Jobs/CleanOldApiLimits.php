<?php

namespace App\Jobs;

use App\Models\ApiRequestLimit;
use Carbon\Carbon;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class CleanOldApiLimits implements ShouldQueue
{
    use InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $threshold = now()->startOfWeek(Carbon::MONDAY); // Get the start of the current week - Monday
        ApiRequestLimit::where('week_start', '<', $threshold)->delete();
    }
}
