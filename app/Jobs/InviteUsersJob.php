<?php

namespace App\Jobs;

use App\Mail\InviteUser;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail;

class InviteUsersJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected array $emails;

    /**
     * Create a new job instance.
     */
    public function __construct($emails = [])
    {
        $this->emails = $emails;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        foreach ($this->emails as $email) {
            $inviteUser = new \App\Models\InviteUser();
            $inviteUser->email = $email;
            Mail::to($email)->send(new InviteUser());
            $inviteUser->is_email_sent = true;
            $inviteUser->is_mass_imported = true;
            $inviteUser->save();
        }
    }
}
