<?php

namespace App\Jobs;

use App\Events\AkinaAIResponse;
use App\Models\AkinaAIMessage;
use App\Models\User;
use App\Services\AkinaAIService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class AkinaAICallJob implements ShouldQueue
{
    use Queueable;

    protected AkinaAIService $akinaAIService;
    protected User $user;
    protected int $conversation_id;
    protected array $messages;
    protected string $user_message;

    /**
     * Create a new job instance.
     */
    public function __construct(User $user, int $conversation_id, string $user_message, array $messages)
    {
        $this->user = $user;
        $this->conversation_id = $conversation_id;
        $this->messages = $messages;
        $this->user_message = $user_message;
        $this->akinaAIService = new AkinaAIService();
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        //sleep(1);
        AkinaAIJob::dispatch($this->user, $this->conversation_id, $this->user_message, $this->messages);
    }
}
