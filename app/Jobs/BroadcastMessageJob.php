<?php

namespace App\Jobs;

use App\Enums\BroadcastMessageType;
use App\Enums\NotificationType;
use App\Models\AdminBroadcastMessage;
use App\Models\FcmTokenList;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class BroadcastMessageJob implements ShouldQueue
{
    use Queueable, Dispatchable, InteractsWithQueue, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct(private readonly AdminBroadcastMessage $message)
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $broadCastMessageId = $this->message->id;
        $title = $this->message->title;
        $body = $this->message->body;
        $type = $this->message->type;
        $type_value = $this->message->type_value;
        $data = [
            "type" => NotificationType::ADMIN_BROADCAST->value,
            "subject" => $title,
        ];

        match ($type) {
            BroadcastMessageType::TOPIC => (function () use ($type_value, $title, $body, $data,$broadCastMessageId) {
                $topic = FcmTokenList::findOrFail($type_value);
                SendNotificationToTopicJob::dispatch($topic->name, $title, $body, $data,$broadCastMessageId);
            })(),
            BroadcastMessageType::USER => (function () use ($type_value, $title, $body, $data) {

                $type_value = is_string($type_value) ? json_decode($type_value, true) : $type_value;
                foreach ($type_value as $userId) {
                    SendSingleUserNotificationJob::dispatch(
                        $userId,
                        $title,
                        $body,
                        $data
                    );
                }
            })(),
        };
    }
}
