<?php

namespace App\Jobs;

use App\Events\AkinaAIResponse;
use App\Models\AkinaAIMessage;
use App\Models\User;
use App\Services\AkinaAIService;
use GuzzleHttp\Client;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Log;

class AkinaAIJob implements ShouldQueue
{
    use Queueable;

    protected AkinaAIService $akinaAIService;
    protected User $user;
    protected int $conversation_id;
    protected array $messages;
    protected string $user_message;

    /**
     * Create a new job instance.
     */
    public function __construct(User $user, int $conversation_id, string $user_message, array $messages)
    {
        $this->user = $user;
        $this->conversation_id = $conversation_id;
        $this->messages = $messages;
        $this->user_message = $user_message;
        $this->akinaAIService = new AkinaAIService();
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $full_response = '';

        $client = new Client();

        $response = $client->request('POST', 'https://5y5ppcmv7xoe2w2nfkofrmmcia0bypuv.lambda-url.us-east-1.on.aws/', [
            'stream' => true,
            'headers' => [
                'Accept' => 'application/json',
            ],
            'json' => [
                'user_query' => $this->user_message,
                'user_id' => $this->user->id,
                'conversation_id' => $this->conversation_id,
            ],
        ]);

        $body = $response->getBody();



        $buffer = '';
        $depth = 0;
        $inString = false;

        while (!$body->eof()) {
            $char = $body->read(1);
            $buffer .= $char;

            // Track nested JSON objects accurately
            if ($char === '"' && substr($buffer, -2) !== '\\"') {
                $inString = !$inString;
            }

            if (!$inString) {
                if ($char === '{') {
                    $depth++;
                } elseif ($char === '}') {
                    $depth--;
                }
            }

            // Full JSON object is ready
            if ($depth === 0 && trim($buffer) !== '') {
                $json = trim($buffer);
                $buffer = '';

                $decoded = json_decode($json, true);
                if (json_last_error() === JSON_ERROR_NONE) {
                    // ✅ Use each object here
                    $text = $decoded['token']['text'] ?? '';
                    $full_response .= $text;
                    broadcast(new AkinaAIResponse($text, $this->conversation_id));
                } else {
                    Log::info(json_encode($decoded));
                }
            }
        }

        AkinaAIMessage::create([
            'conversation_id' => $this->conversation_id,
            'sender_type' => 'ai',
            'user_id' => $this->user->id,
            'content' => $full_response,
        ]);
    }
}
