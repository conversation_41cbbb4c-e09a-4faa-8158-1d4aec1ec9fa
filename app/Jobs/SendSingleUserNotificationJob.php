<?php

namespace App\Jobs;

use App\Models\FCMToken;
use App\Models\UserNotification;
use App\Services\PushNotificationService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class SendSingleUserNotificationJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private $userId;
    private $title;
    private $body;
    private $data;

    public function __construct($userId, $title, $body, $data = [])
    {
        $this->userId = $userId;
        $this->title = $title;
        $this->body = $body;
        $this->data = $data;
    }


    public function handle(PushNotificationService $pushNotificationService): void
    {
        $unreadCount = $pushNotificationService->unreadNotificationsCount($this->userId);
        $this->data['unread_count'] = (string) $unreadCount;

        $notification = $pushNotificationService->saveNotification($this->userId, $this->title, $this->body, $this->data);

        $this->data['notification_id'] = (string) $notification->id;

        $tokens = $pushNotificationService->getUserTokens($this->userId);

        if (!empty($tokens)) {
            $pushNotificationService->sendFirebaseMessage($tokens, $this->title, $this->body, $this->data);
            $notification->is_sent = true;
            $notification->save();
        }
    }
}
