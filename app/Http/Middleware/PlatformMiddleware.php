<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class PlatformMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response) $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $request->merge([
            'client_platform' => 'web',
            'client_platform_os' => 'web',
        ]);

        if ($request->header('platform') === 'mobile') {
            $request['client_platform'] = $request->header('platform');
            $request['client_platform_os'] = $request->header('platform_os') ?? 'ios';
        }

        return $next($request);
    }
}
