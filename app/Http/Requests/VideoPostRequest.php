<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class VideoPostRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'title' => 'required|string',
            'category_id' => 'integer|exists:categories,id',
            'tag_id' => 'integer|exists:tags,id',
            'video_type' => ['required', 'string',
                Rule::in(['self_hosted', 'youtube'])],
            'video_file' => 'required_if:video_type,==,self_hosted|file|mimes:mp4,mov,3gpp,quicktime,x-msvideo,x-ms-wmv,x-flv,application/x-mpegURL,MP2T|max:35000',
            'video_link' => 'required_if:video_type,==,youtube|string',
            'module_id' => 'required|integer|exists:modules,id',
            'thumbnail' => 'required|image|mimes:jpeg,png,jpg|max:2048'
        ];
    }
}
