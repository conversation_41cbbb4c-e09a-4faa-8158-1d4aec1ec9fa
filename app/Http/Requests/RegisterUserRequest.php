<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class RegisterUserRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'first_name' => 'required|string|max:50',
            'last_name' => 'required|string|max:50',
            'user_name' => 'required|string|max:50|unique:users,user_name',
            'email' => 'required|email|unique:users,email|max:50',
            'organization_id' => 'required|exists:organizations,id',
            'password' => 'required|string|max:50',
            'referral_code' => 'nullable|string|max:15',
            'is_student' => 'nullable|boolean',
            'affiliate_code' => [
                'nullable',
                'string',
                'max:20',
                Rule::exists('affiliate_codes', 'code')->where(function ($query) {
                    return $query->where('is_active', true)
                        ->whereNull('redeemed_by');
                })
            ],
        ];
    }

    public function messages(): array
    {
        return [
            'affiliate_code.exists' => 'The affiliate code is invalid or already taken.',
        ];
    }
}
