<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UserNotificationPrefUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'general_information' => 'sometimes|bool',
            'sound' => 'sometimes|bool',
            'vibrate' => 'sometimes|bool',
            'app_updates' => 'sometimes|bool',
            'bill_reminder' => 'sometimes|bool',
            'promotion' => 'sometimes|bool',
            'discount_available' => 'sometimes|bool',
            'payment_request' => 'sometimes|bool',
            'new_service_available' => 'sometimes|bool',
            'new_tips_available' => 'sometimes|bool',
        ];
    }
}
