<?php

namespace App\Http\Requests;

use App\Enums\UserMembershipType;
use App\Enums\UserType;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Enum;

class AdminUserGetRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'limit' => 'sometimes|integer|min:1|max:500',
            'isVerified' => 'nullable|in:0,1',
            'userType' => ['nullable', 'string', new Enum(UserType::class)],
            'userMembershipType' => ['nullable', 'string', new Enum(UserMembershipType::class)],
            'organizationId' => 'nullable|integer|digits_between:1,5',
            'referralId' => 'nullable|integer|digits_between:1,5',
        ];
    }
}
