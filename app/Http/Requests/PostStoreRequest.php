<?php

namespace App\Http\Requests;

use App\Models\Category;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class PostStoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'module_id' => [
                'required',
                'integer',
                Rule::exists('modules', 'id')->where(function ($query) {
                    $query->where('type', 'blog');
                }),
            ],
            'thumbnail' => 'required|image|mimes:jpeg,png,jpg|max:2048',
            'category_ids' => 'nullable|array',
            'category_ids.*' => [
                'integer',
                'exists:categories,id',
                function ($attribute, $value, $fail) {
                    $moduleId = $this->input('module_id');
                    $category = Category::find($value);
                    if (!$category || $category->module_id != $moduleId) {
                        $fail("The category ID $value is not associated with module ID $moduleId.");
                    }
                }
            ],
        ];
    }
}
