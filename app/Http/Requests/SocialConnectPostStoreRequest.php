<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class SocialConnectPostStoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'type' => ['required', 'string',
                Rule::in(['text', 'story', 'post'])],
            'text' => 'required_if:type,==,text|string|max:600',
            'video_file' => 'required_if:type,==,story|file|mimes:mp4,mov,3gpp,quicktime,x-msvideo,x-ms-wmv,x-flv,application/x-mpegURL,MP2T|max:35000',
            'image_file' => 'required_if:type,==,post|image|mimes:jpeg,png,jpg|max:5048',
            'video_thumbnail' => 'sometimes|image|mimes:jpeg,png,jpg|max:2048',
        ];
    }
}
