<?php

namespace App\Http\Requests;

use Illuminate\Database\Query\Builder;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CategoryUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    public function prepareForValidation(): void
    {
        $this->merge([
            'category_id' => $this->route('category_id'),
        ]);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $module_id = request('module_id');
        return [
            'module_id' => 'required|integer|exists:modules,id',
            'layout' => 'string',
            'description' => 'sometimes|string',
            'name' => [
                'required',
                Rule::unique('categories')->where(fn(Builder $query) => $query->where('module_id', $module_id))
            ],
        ];
    }
}
