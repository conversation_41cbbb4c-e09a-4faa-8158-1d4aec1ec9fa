<?php

namespace App\Http\Requests;

use Illuminate\Database\Query\Builder;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ReportReplyRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    public function prepareForValidation(): void
    {
        $this->merge([
            'comment_reply_id' => $this->route('comment_reply_id'),
        ]);

        if ($this->has('report_reason_ids') && is_string($this->report_reason_ids)) {
            $this->merge([
                'report_reason_ids' => array_filter(explode(',', $this->report_reason_ids)),
            ]);
        }
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'report_reason_ids'   => ['required', 'array'],
            'report_reason_ids.*' => ['integer', 'exists:report_reasons,id'],
            'comment_reply_id' => [
                'required',
                'integer',
                'exists:comment_replies,id',
                Rule::unique('reported_replies')->where(fn(Builder $query) => $query->where('reported_by', auth()->user()->id))
            ],
        ];
    }

    public function messages(): array
    {
        return [
            'comment_reply_id.unique' => 'You have already reported this reply.',
        ];
    }
}
