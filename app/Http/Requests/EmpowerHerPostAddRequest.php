<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class EmpowerHerPostAddRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'title' => 'required|string',
            'subtitle' => 'required|string',
            'content' => 'required|string',
            'module_id' => 'required|integer|exists:modules,id',
            'empower_her_type_id' => 'required|integer|exists:empower_her_types,id',
            'thumbnail' => 'required|image|mimes:jpeg,png,jpg|max:2048',
            'button_text'=>'string',
            'button_link'=>'string'
        ];
    }
}
