<?php

namespace App\Http\Requests;

use Illuminate\Database\Query\Builder;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UserProfileUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'user_name' => [
                'sometimes',
                'string',
                Rule::unique('users')->where(fn(Builder $query) => $query->where('id','<>', auth()->user()->id))
            ],
            'education_level' => 'sometimes|string',
            'income_level' => 'sometimes|string',
            'marital_status' => 'sometimes|string',
            'children_option' => 'sometimes|string',
            'interests_option' => 'sometimes|string',
            'joining_motivation_options' => 'sometimes|string',
            'date_of_birth' => 'sometimes|date_format:Y-m-d|before:today',
            'address' => 'nullable|string',
            'phone_number' => 'nullable|string',
            'first_name' => 'sometimes|string',
            'last_name' => 'sometimes|string',
            'nick_name' => 'nullable|string',
            'bio' => 'nullable|string',
            'is_terms_accepted' => 'sometimes|bool',
            'profile_photo' => 'sometimes|image|mimes:jpeg,png,jpg|max:2048',
            'cover_photo' => 'sometimes|image|mimes:jpeg,png,jpg|max:2048',
            'remove_profile_photo' => 'sometimes|boolean',
            'remove_cover_photo' => 'sometimes|boolean',
        ];
    }
}
