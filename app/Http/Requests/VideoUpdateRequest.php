<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class VideoUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'title' => 'required|string',
            'category_id' => 'integer|exists:categories,id',
            'tag_id' => 'integer|exists:tags,id',
            'video_type' => ['required', 'string',
                Rule::in(['self_hosted', 'youtube'])],
            'video_file' => 'sometimes|file|mimes:mp4,mov,3gpp,quicktime,x-msvideo,x-ms-wmv,x-flv,application/x-mpegURL,MP2T|max:35000',
            'video_link' => 'sometimes|string',
            'module_id' => 'required|integer|exists:modules,id',
            'thumbnail' => 'sometimes|image|mimes:jpeg,png,jpg|max:2048'
        ];
    }
}
