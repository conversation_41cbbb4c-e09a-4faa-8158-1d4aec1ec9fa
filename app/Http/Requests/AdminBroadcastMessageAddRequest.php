<?php

namespace App\Http\Requests;

use App\Enums\BroadcastMessageType;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class AdminBroadcastMessageAddRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $rules = [
            'type' => ['required', Rule::in(array_column(BroadcastMessageType::cases(), 'value'))],
            'title' => 'required|string|max:100',
            'body' => 'required|string|max:255',
            'url' => 'nullable|string|max:255',
        ];

        if ($this->input('type') === BroadcastMessageType::USER->value) {
            $rules['type_value'] = [
                'required',
                'array',
                'max:20',
                function ($attribute, $value, $fail) {
                    if (!is_array($value)) {
                        return $fail($attribute . ' must be an array.');
                    }
                    if (!collect($value)->every(fn($id) => is_numeric($id))) {
                        return $fail('All user IDs must be numeric.');
                    }

                    $existing = User::whereIn('id', $value)->pluck('id')->toArray();
                    if (count($existing) !== count($value)) {
                        return $fail('Some user IDs do not exist.');
                    }
                }
            ];
        } elseif ($this->input('type') === BroadcastMessageType::TOPIC->value) {
            $rules['type_value'] = [
                'required',
                'integer',
                Rule::exists('fcm_token_lists', 'id'),
            ];
        } else {
            $rules['type_value'] = ['required', 'string'];
        }

        return $rules;
    }
}
