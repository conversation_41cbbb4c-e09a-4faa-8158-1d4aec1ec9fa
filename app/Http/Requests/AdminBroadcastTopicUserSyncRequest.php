<?php

namespace App\Http\Requests;

use App\Models\FcmTokenList;
use Illuminate\Foundation\Http\FormRequest;

class AdminBroadcastTopicUserSyncRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    public function prepareForValidation(): void
    {
        $this->merge([
            'topic_id' => $this->route('id'),
        ]);
    }


    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $rules = [
            'user_ids' => 'required|array|min:1|max:1000',
            'user_ids.*' => 'required|integer|exists:users,id',
        ];

        $topics = [config('firebase.default_topic')];
        $topics[] = config('firebase.free_user_topic');
        $topics[] = config('firebase.premium_user_topic');

        $topicIds = FCMTokenList::whereIn('name', $topics)->pluck('id')->toArray();
        if (in_array($this->route('id'), $topicIds)) {
            $rules['topic_id'] = [
                function ($attribute, $value, $fail) {

                    return $fail("Default topics can't be  amended.");

                }
            ];
        }
        return $rules;
    }
}
