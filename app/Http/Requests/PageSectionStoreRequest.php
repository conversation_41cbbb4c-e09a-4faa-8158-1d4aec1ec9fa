<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class PageSectionStoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'title' => 'sometimes|string',
            'sub_title' => 'sometimes|string',
            'description' => 'sometimes|string',
            'anchor_text' => 'sometimes|string',
            'anchor_link' => 'sometimes|string',
            'asset_type' => 'required|string',
            'asset_file' => 'sometimes|file'
        ];
    }
}
