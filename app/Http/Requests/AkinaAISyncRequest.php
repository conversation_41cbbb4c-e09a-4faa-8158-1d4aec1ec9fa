<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class AkinaAISyncRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'conversation_id' => 'required|string|max:60',
            'user_id' => 'required|integer|exists:users,id',
            'user_query' => 'required|string',
            'llm_response' => 'required|string',
            'question_id' => 'required|string|max:60|unique:akina_ai_messages,external_message_id',
            'answer_id' => 'required|string|max:60|unique:akina_ai_messages,external_message_id',
        ];
    }
}
