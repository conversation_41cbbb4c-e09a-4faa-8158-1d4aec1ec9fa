<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class EventAddRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'title' => 'required|string',
            'sub_title' => 'required|string',
            'content' => 'required|string',
            'module_id' => 'required|integer|exists:modules,id',
            'location_id' => 'required|integer|exists:locations,id',
            'is_akina_event' => 'required|integer',
            'thumbnail' => 'required|image|mimes:jpeg,png,jpg|max:2048',
            'event_date'=>'required|date_format:Y-m-d H:i:s',
            'button_text'=>'string',
            'button_link'=>'string'
        ];
    }
}
