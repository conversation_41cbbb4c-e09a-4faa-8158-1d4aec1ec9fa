<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class MusicUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'title' => 'required|string',
            'category_id' => 'integer|exists:categories,id',
            'audio_file' => 'sometimes|file|mimes:audio/mpeg,mp3,wav|max:50000',
            'thumbnail' => 'sometimes|image|mimes:jpeg,png,jpg|max:2048'
        ];
    }
}
