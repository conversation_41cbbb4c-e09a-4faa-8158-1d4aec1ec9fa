<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class SocialConnectPostUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'type' => ['required', 'string',
                Rule::in(['text', 'story', 'post'])],
            'text' => 'nullable|string|max:600',
            'video_file' => 'sometimes|file|mimes:mp4,mov,3gpp,quicktime,x-msvideo,x-ms-wmv,x-flv,application/x-mpegURL,MP2T|max:35000',
            'image_file' => 'sometimes|image|mimes:jpeg,png,jpg|max:5048',
        ];
    }
}
