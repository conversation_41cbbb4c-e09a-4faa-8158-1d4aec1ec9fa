<?php

namespace App\Http\Controllers;

use App\Http\Requests\SupportStoreRequest;
use App\Mail\SupportForm;
use App\Mail\UserRegistration;
use App\Models\UserSupport;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;

class UserSupportController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $support_data = UserSupport::orderBy('id', 'ASC')->get();

        return response()->json(
            [
                'success' => true,
                'support_data' => $support_data,
            ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(SupportStoreRequest $request): \Illuminate\Http\JsonResponse
    {
        $data = $request->only([
            'name',
            'email',
            'message',
        ]);

        $support_data = UserSupport::create(
            $data
        );


        Mail::to('<EMAIL>')->send(new SupportForm($support_data['email'], $support_data['name'], $support_data['message']));

        return response()->json(
            [
                'success' => true,
                'message' => 'Your request is submitted successfully',
                'support_data' => $support_data,
            ]);
    }
}
