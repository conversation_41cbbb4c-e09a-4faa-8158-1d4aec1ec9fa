<?php

namespace App\Http\Controllers;

use App\Models\BookMark;
use App\Models\Category;
use App\Models\Like;
use App\Models\Module;
use App\Models\Music;
use App\Models\Tag;
use App\Services\MusicService;
use App\Traits\RestrictFreeUser;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class MusicController extends Controller
{
    use RestrictFreeUser;

    private MusicService $musicService;

    public function __construct(MusicService $musicService)
    {
        $this->musicService = $musicService;
    }

    /**
     * Display a listing of the resource.
     */
    public function index(string $module_id, Request $request): \Illuminate\Http\JsonResponse
    {
        $module = Module::where('type', 'music')->where('id', $module_id)->firstOrFail();

        $tags = Tag::where('module_id', $module_id)->with('music')->whereHas('music')->get();
        $categories = Category::where('module_id', $module_id)->with('music', function ($query) {
            $query->orderBy('id', 'asc');
        })->whereHas('music')->orderBy('created_at', 'ASC')->get();

        return response()->json(
            [
                'success' => true,
                'module' => $module,
                'categories' => $categories,
                'tags' => $tags,
            ]);
    }

    public function getByCategory(string $module_id, string $category_id, Request $request): \Illuminate\Http\JsonResponse
    {
        $music = Music::where('category_id', $category_id)
            ->with(['category', 'tag'])
            ->orderBy('id', 'desc')
            ->get();

        return response()->json(
            [
                'success' => true,
                'music' => $music,
            ]);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $module_id, string $music_id)
    {
        return $this->musicService->getDetail($music_id, $module_id);
    }

    public function like(Request $request, string $module_id, string $music_id): JsonResponse
    {
        $this->restrictFree();

        $music = Music::findOrFail($music_id);
        $user_id = Auth::user()->id;

        if ($is_liked = $music->likes()->where('user_id', $user_id)->first()) {
            $is_liked->delete();
            $status = 'unlike';
        } else {
            $like = new Like(['module_id' => $module_id, 'user_id' => $user_id]);
            $music->likes()->save($like);
            $status = 'like';
        }

        return response()->json(
            [
                'success' => true,
                'message' => "Music $status successful",
                'status' => $status,
                'music' => $this->musicService->getDetail($music_id, $module_id)
            ]);
    }

    public function bookMark(Request $request, string $module_id, string $music_id): JsonResponse
    {
        $this->restrictFree();

        $music = Music::findOrFail($music_id);
        $user_id = Auth::user()->id;

        if ($is_book_marked = $music->bookmarks()->where('user_id', $user_id)->first()) {
            $is_book_marked->delete();
            $status = 'unsaved';
        } else {
            $bookmark = new BookMark(['module_id' => $module_id, 'user_id' => $user_id]);
            $music->bookmarks()->save($bookmark);
            $status = 'saved';
        }

        return response()->json(
            [
                'success' => true,
                'message' => "Music $status successful",
                'status' => $status,
                'music' => $this->musicService->getDetail($music_id, $module_id)
            ]);
    }
}
