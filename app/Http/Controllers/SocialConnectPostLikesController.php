<?php

namespace App\Http\Controllers;

use App\Jobs\SendSingleUserNotificationJob;
use App\Models\Like;
use App\Models\SocialConnectPost;
use App\Services\PushNotificationService;
use App\Services\SocialConnectPostService;
use App\Traits\RestrictFreeUser;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class SocialConnectPostLikesController extends Controller
{
    use RestrictFreeUser;
    private SocialConnectPostService $socialConnectPostService;
    private PushNotificationService $pushNotificationService;

    public function __construct(SocialConnectPostService $socialConnectPostService, PushNotificationService $pushNotificationService)
    {
        $this->socialConnectPostService = $socialConnectPostService;
        $this->pushNotificationService = $pushNotificationService;
    }

    /**
     * Store a newly created resource in storage.
     */
    public function like(Request $request, string $post_id): JsonResponse
    {
        $this->restrictFree();

        $post = SocialConnectPost::findOrFail($post_id);
        $user_id = Auth::user()->id;
        $notificationResponse = null;

        if ($is_liked = $post->likes()->where('user_id', $user_id)->first()) {
            $is_liked->delete();
            $status = 'unlike';
        } else {
            $like = new Like(['module_id' => $post->module_id, 'user_id' => $user_id]);
            $post->likes()->save($like);
            $status = 'like';

            if ($post->user_id !== $user_id) {
                $postOwner = $post->user;
                $likedBy = Auth::user();
                $likerFullName = "{$likedBy->first_name} {$likedBy->last_name}";

                SendSingleUserNotificationJob::dispatch(
                    $postOwner->id,
                    "{$likerFullName} liked your post",
                    "{$likerFullName} liked your post: " . Str::limit($post->text, 20),
                    [
                        'post_id' => (string)$post->id,
                        'liked_by' => (string)$likedBy->id,
                        'url' => "akinaconnect://social-connect-detail/{$post->id}",
                        'web_url' => "/social-connect/post/{$post->id}",
                    ]
                );
            }
        }

        return response()->json(
            [
                'success' => true,
                'message' => "SC post $status successful",
                'status' => $status,
                'post' => $this->socialConnectPostService->getDetail($post_id),
            ]);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
