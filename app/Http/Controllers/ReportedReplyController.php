<?php

namespace App\Http\Controllers;

use App\Http\Requests\ReportReplyRequest;
use App\Models\ReportedReply;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ReportedReplyController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }


    /**
     * Store a newly created resource in storage.
     */
    public function store(ReportReplyRequest $request)
    {
        $reportedReply = new ReportedReply();
        $reportedReply->comment_reply_id = $request->comment_reply_id;
        $reportedReply->reported_by = Auth::user()->id;
        $reportedReply->report_reason_ids = $request->report_reason_ids;
        $reportedReply->save();

        return response()->json(
            [
                'success' => true,
                'message' => "Reply reported successfully."
            ]);
    }

    /**
     * Display the specified resource.
     */
    public function show(ReportedReply $reportedReply)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, ReportedReply $reportedReply)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ReportedReply $reportedReply)
    {
        //
    }
}
