<?php

namespace App\Http\Controllers;

use App\Http\Requests\TagPostRequest;
use App\Models\Tag;
use Illuminate\Http\Request;

class TagsController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(): \Illuminate\Http\JsonResponse
    {
        $tags = Tag::all();

        return response()->json(
            [
                'success' => true,
                'tags' => $tags,
            ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(TagPostRequest $request): \Illuminate\Http\JsonResponse
    {
        $tag = new Tag();

        $tag->name = $request->name;
        $tag->module_id = $request->module_id;
        $tag->save();

        return response()->json(
            [
                'success' => true,
                'message' => 'Tag added successfully',
                'tag_data' => $tag,
            ]);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
