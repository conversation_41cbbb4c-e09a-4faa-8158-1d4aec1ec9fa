<?php

namespace App\Http\Controllers;

use App\Models\FcmTokenList;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class FcmTokenListController extends Controller
{
    public function store(Request $request)
    {
        $validated_name = $request->validate([
            'name' => 'required|string|unique:fcm_token_lists,name'
        ]);
        $slug = Str::slug($validated_name['name']);

        $list = FcmTokenList::create(['name' => $slug]);

        return response()->json([
            'success' => true,
            'message' => 'FCM token list created successfully.',
            'data' => $list
        ]);
    }
}
