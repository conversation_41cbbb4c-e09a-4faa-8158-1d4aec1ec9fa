<?php

namespace App\Http\Controllers;

use App\Http\Requests\EmpowerHerPostAddRequest;
use App\Models\EmpowerHerPost;
use App\Models\EmpowerHerPostImage;
use App\Models\EmpowerHerType;
use Carbon\Carbon;
use Illuminate\Support\Str;

class EmpowerHerPostsController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(string $module_id, string $type_id)
    {
        $type = EmpowerHerType::findOrFail($type_id);
        $empowerHerPosts = EmpowerHerPost::where('empower_her_type_id', $type_id)->get();

        return response()->json(
            [
                'success' => true,
                'posts' => $empowerHerPosts,
                'type' => $type,
            ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(EmpowerHerPostAddRequest $request): \Illuminate\Http\JsonResponse
    {
        $data = $request->only([
            'title',
            'content',
            'subtitle',
            'module_id',
            'button_text',
            'button_link',
            'empower_her_type_id',
        ]);

        if ($request->hasFile('thumbnail')) {

            $extension = request()->file('thumbnail')->getClientOriginalExtension();
            $image_name = time() . '_' . Str::slug($request->title) . '-thumbnail.' . $extension;

            $path = $request->file('thumbnail')->storeAs(
                'empower_her_photos',
                $image_name,
                's3'
            );

            $data['thumbnail'] = \config('filesystems.disks.s3.url') . $path;
        }

        $post_data = EmpowerHerPost::create(
            $data
        )->toArray();

        if ($request->hasFile('gallery')) {
            $imageData = [];
            $i = 0;
            $now = Carbon::now();
            foreach ($request->gallery as $gallery) {

                $extension = $gallery->getClientOriginalExtension();
                $image_name = time() . '_' . Str::slug($request->title) . '-' . $i . '-gallery.' . $extension;

                $path = $gallery->storeAs(
                    'empower_her_photos_2',
                    $image_name,
                    's3'
                );
                $imageData[$i]['empower_her_post_id'] = $post_data['id'];
                $imageData[$i]['image_url'] = \config('filesystems.disks.s3.url') . $path;
                $imageData[$i]['created_at'] = $now;
                $imageData[$i]['updated_at'] = $now;
                $i++;
            }

            EmpowerHerPostImage::insert(
                $imageData
            );
        }

        return response()->json(
            [
                'success' => true,
                'message' => 'post added successfully',
                'post_data' => $post_data,
            ]);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $module_id, string $type_id, string $post_id)
    {
        $post = EmpowerHerPost::with('images')
            ->findOrFail($post_id);

        return response()->json($post);
    }
}
