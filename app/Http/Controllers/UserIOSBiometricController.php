<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\UserIOSBiometricKey;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class UserIOSBiometricController extends Controller
{
    public function store(string $user_id, Request $request)
    {
        $validator = Validator::make($request->all(), [
            'key' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $user = Auth::user();
        if ($user->id != $user_id) {
            return response()->json(
                [
                    'success' => false,
                    'message' => 'You are not authorized to make this request.',
                ], 403);
        }
        $oldUserIOSBiometric = UserIOSBiometricKey::where('user_id', $user_id)->where('is_active', true)->first();
        if ($oldUserIOSBiometric) {
            $oldUserIOSBiometric->is_active = false;
            $oldUserIOSBiometric->save();
        }

        $userIOSBiometric = new UserIOSBiometricKey();
        $userIOSBiometric->pub_key = $request->key;
        $userIOSBiometric->user_id = $user->id;
        $userIOSBiometric->save();

        return response()->json(
            [
                'success' => true,
                'message' => 'User key is  registered successfully',
            ]);
    }

    public function verify(string $user_id, Request $request)
    {
        $validator = Validator::make($request->all(), [
            'signature' => 'required|string',
            'payload' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $user = User::findOrFail($user_id);

        $userIOSBiometricKey = UserIOSBiometricKey::where('user_id', $user->id)->where('is_active', true)->first();
        if (!$userIOSBiometricKey) {
            return response()->json([
                'success' => false,
                'pub_key_exists' => false,
                'message' => "User key doesn't exist",
            ]);
        }
        $publicKey = $userIOSBiometricKey->pub_key;

        $signature = $request->signature;
        $payload = $request->payload;

        $isVerified = $this->verifyKeyAndSignature($payload, $publicKey, $signature);

        if ($isVerified) {
            $token = $user->createToken($user->first_name, ['role:user']);
            $response = [
                'success' => true,
                'pub_key_exists' => true,
                'message' => 'User verified successfully.',
                'token' => $token->plainTextToken,
                'user' => $user
            ];
        } else {
            $response = [
                'success' => false,
                'pub_key_exists' => true,
                'message' => "Authentication failed. Please try again.",
            ];
        }
        return response()->json($response);
    }

    public function verifyKeyAndSignature(string $payload, string $publicKey, string $signature): bool
    {
        // Format the public key
        $formattedPublicKey = "-----BEGIN PUBLIC KEY-----\n" . wordwrap($publicKey, 64, "\n", true) . "\n-----END PUBLIC KEY-----";

        // Decode the signature from Base64
        $decodedSignature = base64_decode($signature);

        // Create a verification resource
        $publicKeyResource = openssl_get_publickey($formattedPublicKey);

        if ($publicKeyResource === false) {
            return false;
        }

        // Verify the signature using RSA-SHA256
        $isVerified = openssl_verify($payload, $decodedSignature, $publicKeyResource, OPENSSL_ALGO_SHA256);

        return (bool)$isVerified;
    }
}
