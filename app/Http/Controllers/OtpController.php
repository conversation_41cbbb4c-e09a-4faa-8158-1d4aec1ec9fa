<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Services\OTPService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class OtpController extends Controller
{
    private OTPService $OtpService;

    public function __construct(OTPService $OtpService)
    {
        $this->OtpService = $OtpService;
    }

    public function resendOtp(Request $request): \Illuminate\Http\JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email|exists:users,email',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()],422);
        }

        $user = User::where('email', $request->email)->firstOrFail();

        $this->OtpService->createAndSendOTP($user);

        return response()->json(
            [
                'success' => true,
                'message' => 'New otp has been sent to your email.'
            ]
        );
    }
}
