<?php

namespace App\Http\Controllers;

use App\Http\Requests\UserSearchRequest;
use App\Http\Resources\UserResourceCollection;
use App\Models\BlockedUser;
use App\Models\DeletedUser;
use App\Models\SocialConnectPost;
use App\Models\User;
use App\Models\UserFollower;
use App\Services\PushNotificationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class UsersController extends Controller
{
    protected $pushNotificationService;

    public function __construct(PushNotificationService $pushNotificationService)
    {
        $this->pushNotificationService = $pushNotificationService;
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $limit = $request->limit ?? 10;
        $user = Auth::user();
        $blockedUserIds = BlockedUser::where('blocked_by', $user->id)
            ->pluck('user_id');
        $blockedUserIds->add($user->id);

        $followingIdsArray = $user ? $user->followings()->pluck('user_id') : collect();
        $users = User::whereNotIn('id', $blockedUserIds)->paginate($limit);

        return new UserResourceCollection($users, $followingIdsArray);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function search(UserSearchRequest $request): UserResourceCollection
    {
        $limit = $request->limit ?? 10;
        $user = Auth::user();
        $followingIdsArray = $user ? $user->followings()->pluck('user_id') : collect();

        $blockedUserIds = BlockedUser::where('blocked_by', $user->id)
            ->pluck('user_id');


        $usersQuery = User::whereNotIn('id', $blockedUserIds);
        $searchValues = explode(" ", $request->search_value);
        $firstName = $searchValues[0];
        $lastName = $searchValues[1] ?? $searchValues[0];

        $usersQuery = $usersQuery->where(function ($query) use ($firstName, $lastName) {
            $query->where('first_name', 'like', "%" . $firstName . "%")
                ->orWhere('last_name', 'like', "%" . $lastName . "%")
                ->orWhere('email', 'like', "%" . $lastName . "%");
        });

        $users = $usersQuery->whereNot('id', $user->id)->paginate($limit);

        return new UserResourceCollection($users, $followingIdsArray);
    }

    /**
     * Display the specified resource.
     */
    public function profile(): \Illuminate\Http\JsonResponse
    {
        $user = Auth::user();
        $users = User::where('id', $user->id)
            ->with('profile')
            ->with('fcmTokens')
            ->withCount('followings')
            ->withCount('followers')
            ->withCount('socialConnectLikes')
            ->first();

        $posts = SocialConnectPost::where('user_id', $user->id)->orderByDesc('created_at')->get();
        $notifications_count = $this->pushNotificationService->unreadNotificationsCount($user->id);

        return response()->json(['status' => true, 'user' => $users, 'posts' => $posts, 'notifications_count' => $notifications_count]);
    }

    public function profileDetails(string $user_id): \Illuminate\Http\JsonResponse
    {
        $user = User::findOrFail($user_id);
        $users = User::where('id', $user->id)
            ->with('profile')
            ->withExists(['blockedUsers AS blocked_user_exist'=>function ($query) {
                $query->where('blocked_by', Auth::user()->id);
            }])
            ->withCount('followings')
            ->withCount('followers')
            ->withCount('socialConnectLikes')
            ->first();

        $posts = SocialConnectPost::where('user_id', $user->id)->orderByDesc('created_at')->get();

        $loggedInUser = Auth::user();
        $is_followed = false;
        $isFollower = UserFollower::where('follower_id', $loggedInUser->id)->where('user_id', $user->id)->first();

        if ($isFollower) {
            $is_followed = true;
        }
        $notifications_count = $this->pushNotificationService->unreadNotificationsCount($user_id);

        $users['is_followed'] = $is_followed;

        return response()->json([
            'status' => true,
            'user' => $users,
            'posts' => $posts,
            'notifications_count' => $notifications_count
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy()
    {
        $user = Auth::user();

        $deletedUser = new DeletedUser();
        $deletedUser->user_id = $user->id;
        $deletedUser->first_name = $user->first_name;
        $deletedUser->last_name = $user->last_name;
        $deletedUser->email = $user->email;
        $deletedUser->user_type = $user->user_type;
        $deletedUser->user_membership_type = $user->user_membership_type;
        $deletedUser->stripe_id = $user->stripe_id;
        $deletedUser->user_created_at = $user->created_at;
        $deletedUser->save();

        $this->pushNotificationService->unSubscribeFromAllTopics($user);
        $user->delete();

        return response()->json([
            'status' => true,
            'message' => 'user deleted successfully',
        ]);
    }
}

