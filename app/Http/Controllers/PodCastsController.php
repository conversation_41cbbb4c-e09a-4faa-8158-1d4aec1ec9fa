<?php

namespace App\Http\Controllers;

use App\Http\Requests\PodCastPostRequest;
use App\Http\Resources\PodCastResource;
use App\Models\BookMark;
use App\Models\Category;
use App\Models\Like;
use App\Models\Module;
use App\Models\PodCast;
use App\Models\PodCastLink;
use App\Models\Tag;
use App\Services\PodCastService;
use App\Traits\RestrictFreeUser;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class PodCastsController extends Controller
{
    use RestrictFreeUser;
    private PodCastService $podCastService;

    public function __construct(PodCastService $podCastService)
    {
        $this->podCastService = $podCastService;
    }

    /**
     * Display a listing of the resource.
     */
    public function index(string $module_id, Request $request): \Illuminate\Http\JsonResponse
    {
        $isWeb = $request->query('request_source') === 'web';

        if ($isWeb) {
            return $this->withPagination($module_id, $request);
        }
        $module = Module::with('metas')->where('type', 'podcast')->where('id', $module_id)->firstOrFail();

        $tags = Tag::where('module_id', $module_id)->with('podcasts', function ($q) {
            $q->select('id', 'category_id', 'module_id', 'tag_id', 'title', 'guid', 'audio_link', 'thumbnail', 'pub_date', 'created_at', 'updated_at')->orderBy('id', 'desc');
        })->whereHas('podcasts')->get();
        $categories = Category::where('module_id', $module_id)->with('podcasts', function ($q) {
            $q->select('id', 'category_id', 'module_id', 'tag_id', 'title', 'guid', 'audio_link', 'thumbnail', 'pub_date', 'created_at', 'updated_at')->orderBy('id', 'desc');
        })->whereHas('podcasts')->get();

        foreach ($categories as $category) {
            foreach ($category->podcasts as $podcast) {
                $podcast->audio_link = $this->podCastService->getDestinationUrl($podcast->audio_link);
            }
        }

        return response()->json(
            [
                'success' => true,
                'module' => $module,
                'categories' => $categories,
                'tags' => $tags,
            ]);
    }

    private function withPagination(string $module_id, Request $request): \Illuminate\Http\JsonResponse
    {
        $limit = $request->limit ?? 10;

        $module = Module::with('metas')->where('type', 'podcast')->where('id', $module_id)->firstOrFail();

        $categories = Category::where('module_id', $module_id)->withCount('podcasts')->with('podcasts', function ($q) use ($limit) {
            $q->select('id', 'category_id', 'module_id', 'tag_id', 'title', 'description', 'guid', 'audio_link', 'thumbnail', 'pub_date', 'created_at', 'updated_at')
                ->orderBy('id', 'desc')->limit($limit);
        })->whereHas('podcasts')->get();

        foreach ($categories as $category) {
            foreach ($category->podcasts as $podcast) {
                $podcast->audio_link = $this->podCastService->getDestinationUrl($podcast->audio_link);
                $podcast->description = $this->podCastService->getFirstWordsFromHTML($podcast->description, 30);
            }
        }

        return response()->json(
            [
                'success' => true,
                'module' => $module,
                'categories' => $categories
            ]);
    }

    public function search(Request $request): \Illuminate\Http\JsonResponse
    {
        $searchValue = $request->input('q');
        $podcasts = PodCast::where('title', 'like', "%" . $searchValue . "%")
            ->orWhere('description', 'like', "%" . $searchValue . "%")->get();

        return response()->json($podcasts);
    }

    public function getByCategory(string $module_id, string $category_id, Request $request): \Illuminate\Http\JsonResponse
    {
        $limit = $request->limit ?? 10;
        $podcasts = PodCast::where('category_id', $category_id)->orderBy('id', 'desc')->paginate($limit);


        foreach ($podcasts as $podcast) {
            $podcast->audio_link = $this->podCastService->getDestinationUrl($podcast->audio_link);
        }

        return response()->json(
            [
                'success' => true,
                'podcasts' => $podcasts,

            ]);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $module_id, string $podcast_id): PodCastResource
    {
        return $this->podCastService->getDetail($podcast_id, $module_id);
    }

    public function like(Request $request, string $module_id, string $podcast_id): JsonResponse
    {
        $this->restrictFree();

        $podcast = PodCast::findOrFail($podcast_id);
        $user_id = Auth::user()->id;

        if ($is_liked = $podcast->likes()->where('user_id', $user_id)->first()) {
            $is_liked->delete();
            $status = 'unlike';
        } else {
            $like = new Like(['module_id' => $module_id, 'user_id' => $user_id]);
            $podcast->likes()->save($like);
            $status = 'like';
        }

        return response()->json(
            [
                'success' => true,
                'message' => "Podcast $status successful",
                'status' => $status,
                'podcast' => $this->podCastService->getDetail($podcast_id, $module_id),
            ]);
    }

    public function bookMark(Request $request, string $module_id, string $podcast_id): JsonResponse
    {
        $this->restrictFree();

        $podcast = PodCast::findOrFail($podcast_id);
        $user_id = Auth::user()->id;

        if ($is_book_marked = $podcast->bookmarks()->where('user_id', $user_id)->first()) {
            $is_book_marked->delete();
            $status = 'unsaved';
        } else {
            $bookmark = new BookMark(['module_id' => $module_id, 'user_id' => $user_id]);
            $podcast->bookmarks()->save($bookmark);
            $status = 'saved';
        }

        return response()->json(
            [
                'success' => true,
                'message' => "Podcast $status successful",
                'status' => $status,
                'podcast' => $this->podCastService->getDetail($podcast_id, $module_id),
            ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(PodCastPostRequest $request): \Illuminate\Http\JsonResponse
    {
        $module = Module::where('type', 'podcast')->firstOrFail();
        $urlArr = PodCastLink::where('is_active', true)->get();
        $podcastsArray = [];
        $guidArr = [];
        foreach ($urlArr as $url) {
            $url = $url->link;
            $feedXML = simplexml_load_file($url);

            if (empty($feedXML)) {
                $ch = curl_init($url);

                curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 6.2; WOW64; rv:17.0) Gecko/20100101 Firefox/17.0');
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
                curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);

                $result = curl_exec($ch);

                if (substr($result, 0, 5) == "<?xml") {
                    $feedXML = simplexml_load_string($result);
                }

                curl_close($ch);
            }

            if ($feedXML->channel->item) {
                $feed_item_itunes1 = $feedXML->channel->children('itunes', true);
                //   print_r($feed_item_itunes1->category->attributes()->text);
                $categoryArray = [];
                $categoryArray['name'] = $feed_item_itunes1->category->attributes() ? (string)$feed_item_itunes1->category->attributes()->text : 'uncategorized';
                $categoryArray['module_id'] = $module->id;
                $categoryArray['layout'] = 'carousel';

                $category = Category::firstOrCreate($categoryArray);

                for ($i = 0; $i < count($feedXML->channel->item); $i++) {
                    $feed_item = $feedXML->channel->item[$i];
                    $feed_item_itunes = $feed_item->children('itunes', true);

                    // print_r($feedXML->channel);

                    $guidArr[$i]['guid'] = (string)$feedXML->channel->item[$i]->guid;
                    $podcastsArray[$i]['guid'] = (string)$feedXML->channel->item[$i]->guid;
                    $podcastsArray[$i]['title'] = (string)$feedXML->channel->item[$i]->title;
                    //$podcastsArray[$i]['description'] = (string)$feedXML->channel->item[$i]->description;
                    $podcastsArray[$i]['audio_link'] = (string)$feedXML->channel->item[$i]->enclosure['url'];
                    $podcastsArray[$i]['thumbnail'] = $feed_item_itunes->image->attributes() ? (string)$feed_item_itunes->image->attributes()->href : '';
                    $podcastsArray[$i]['category_id'] = $category->id;

                }
            }
            PodCast::upsert($podcastsArray, ['guid'], ['title']);
        }
        return response()->json(
            [
                'success' => true,
                'message' => 'Podcast imported successfully',
            ]);
    }
}
