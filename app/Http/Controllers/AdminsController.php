<?php

namespace App\Http\Controllers;

use App\Models\Admin;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;

class AdminsController extends Controller
{
    public function register(Request $request): \Illuminate\Http\JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required',
            'email' => 'required|email|unique:admins,email',
            'password' => 'required',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $admin = new Admin();
        $admin->name = $request->name;
        $admin->email = $request->email;
        $admin->email_verified_at = Carbon::now();
        $admin->password = bcrypt($request->password);
        $admin->save();

        $token = $admin->createToken($admin->name, ['ability:canCallFromWP']);

        return response()->json(
            [
                'success' => true,
                'message' => 'Admin registered successfully. ',
                'admin' => $admin,
                'token' => $token->plainTextToken,
            ]
        );
    }

    public function login(Request $request): \Illuminate\Http\JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
            'password' => 'required',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $admin = Admin::where('email', $request->email)->first();

        if ($admin && Hash::check($request->password, $admin->password)) {
            $admin = Admin::where('email', $request->email)->firstOrFail();
            $token = $admin->createToken($admin->name, ['ability:admin']);

            return response()->json(
                [
                    'success' => true,
                    'message' => 'Admin logged in successfully. ',
                    'admin' => $admin,
                    'role' => 'super_admin',
                    'token' => $token->plainTextToken,
                ]
            );
        }

        return response()->json(
            [
                'success' => false,
                'message' => 'Invalid credentials.'
            ], 401);
    }
}
