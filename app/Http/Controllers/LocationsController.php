<?php

namespace App\Http\Controllers;

use App\Http\Requests\LocationPostRequest;
use App\Models\Location;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class LocationsController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): \Illuminate\Http\JsonResponse
    {
        $locations = Location::all();

        return response()->json(
            [
                'success' => true,
                'locations' => $locations,
            ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(LocationPostRequest $request): \Illuminate\Http\JsonResponse
    {

        $data = $request->only([
            'name',
            'sub_title',
            'module_id',
            'thumbnail',
            'is_akina_event',
        ]);

        if ($request->hasFile('thumbnail')) {

            $extension = request()->file('thumbnail')->getClientOriginalExtension();
            $image_name = time() . '_' . Str::slug($request->title) . '-thumbnail.' . $extension;

            $path = $request->file('thumbnail')->storeAs(
                'location_photos',
                $image_name,
                's3'
            );

            $data['thumbnail'] = \config('filesystems.disks.s3.url') . $path;
        }

        $post_data = Location::create(
            $data
        )->toArray();

        return response()->json(
            [
                'success' => true,
                'message' => 'Location added successfully',
                'location_data' => $post_data,
            ]);
    }
}
