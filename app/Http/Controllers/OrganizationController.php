<?php

namespace App\Http\Controllers;

use App\Models\Organization;
use Illuminate\Http\JsonResponse;

class OrganizationController extends Controller
{
    public function index(): JsonResponse
    {
        $organizations = Organization::where('is_active', true)
            ->orderBy('order', 'asc')
            ->get();

        return response()->json([
            'status' => 'success',
            'organizations' => $organizations,
        ]);
    }
}
