<?php

namespace App\Http\Controllers;

use App\Models\Module;
use App\Models\NewsSource;
use App\Services\FetchNewsService;
use Illuminate\Http\Request;

class NewsResourcesController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(string $module_id, Request $request): \Illuminate\Http\JsonResponse
    {
        $module = Module::where('type', 'news')->where('id', $module_id)->firstOrFail();

        $news_sources = NewsSource::where('module_id', $module_id)->orderBy('order','asc')->get();

        return response()->json(
            [
                'success' => true,
                'module' => $module,
                'news_sources' => $news_sources,
            ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): \Illuminate\Http\JsonResponse
    {
        $fetchNewsService = new FetchNewsService();

        $news_sources = $fetchNewsService->getNewsSource();
        $data = [];

        $i = 0;
        foreach ($news_sources['data'] as $source) {

            $data[$i]['external_id'] = $source['id'];
            $data[$i]['module_id'] = $request->module_id;
            $data[$i]['name'] = $source['name'];
            $data[$i]['url'] = $source['url'];
            $data[$i]['base_href'] = $source['base_href'];
            $data[$i]['thumbnail'] = $source['logo_uri'];
            $i++;
        }

        NewsSource::upsert($data, ['external_id'], ['name']);

        return response()->json(
            [
                'success' => true,
                'message' => 'News sources imported successfully',
            ]);
    }
}
