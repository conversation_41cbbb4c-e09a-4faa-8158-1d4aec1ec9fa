<?php

namespace App\Http\Controllers;

use App\Http\Requests\InviteMassUsersRequest;
use App\Http\Requests\InviteUsersRequest;
use App\Jobs\InviteUsersJob;
use App\Models\InviteUser;
use App\Models\User;
use App\Models\UserCountThreshold;
use Illuminate\Http\JsonResponse;

class InviteUsersController extends Controller
{
    public function inviteUser(InviteUsersRequest $request): JsonResponse
    {
        $userCountThreshold = UserCountThreshold::firstOrFail();
        $usersCount = User::count();

        if ($userCountThreshold->allowed_user_count > $usersCount) {
            return response()->json(
                [
                    'success' => false,
                    'message' => 'User invitation is not allowed at this time.',
                ]);
        }

        $email = $request->email;

        $inviteUser = new InviteUser();
        $inviteUser->email = $email;
        $inviteUser->save();

        return response()->json(
            [
                'success' => true,
                'message' => 'User successfully added in invite list.',
            ]);
    }

    public function inviteMassUsers(InviteMassUsersRequest $request): JsonResponse
    {
        InviteUsersJob::dispatch($request->emails);


        return response()->json(
            [
                'success' => true,
                'message' => 'Users invited successfully',
            ]);
    }
}
