<?php

namespace App\Http\Controllers;

use App\Http\Requests\AkinaAISyncRequest;
use App\Models\AkinaAIConversation;
use App\Services\AkinaAIService;
use Illuminate\Http\JsonResponse;

class AkinaAIConversationsSyncController extends Controller
{
    protected AkinaAIService $akinaAIService;

    public function __construct(AkinaAIService $akinaAIService)
    {
        $this->akinaAIService = $akinaAIService;
    }

    /*
     * Sync and store conversation
     */
    public function store(AkinaAISyncRequest $request)
    {
        $data = $request->validated();

        $conversationId = $this->akinaAIService->getConversationIdFromExternalId($data);
        $isMessageCreated = $this->akinaAIService->createMessage($data, $conversationId);

        return response()->json(['status' => true, 'message' => 'Message saved successfully.', 'conversation_id' => $conversationId]);
    }

    public function show(string $moduleId, string $externalConversationId): JsonResponse
    {
        $akinaConversation = AkinaAIConversation::where('external_conversation_id', $externalConversationId)
            ->with('messages')
            ->firstOrFail();

        return response()->json(['status' => true, 'conversation' => $akinaConversation]);

    }
}
