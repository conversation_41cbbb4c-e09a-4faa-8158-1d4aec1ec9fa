<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Services\OTPService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class ForgotPasswordController extends Controller
{
    private OTPService $OtpService;

    public function __construct(OTPService $OtpService)
    {
        $this->OtpService = $OtpService;
    }

    public function forgotPassword(Request $request): \Illuminate\Http\JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email|exists:users,email',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()],422);
        }

        $user = User::where('email', $request->email)->firstOrFail();

        $this->OtpService->createAndSendOTP($user);

        return response()->json(['success' => true, 'message' => 'An OTP has been sent to your email, please verify before proceeding further.']);
    }

    public function verify(Request $request): \Illuminate\Http\JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email|exists:users,email',
            'otp' => 'required',
            'new_password' => 'required|confirmed',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()],422);
        }

        $user = User::where('email', $request->email)->firstOrFail();

        if ($this->OtpService->verifyOTP($user, $request->otp)) {

            $user->password = bcrypt($request->new_password);
            $user->save();

            return response()->json(
                [
                    'success' => true,
                    'message' => 'User password reset successfully.',
                ]
            );
        }

        return response()->json(['success' => false, 'message' => 'OTP is incorrect.']);
    }
}
