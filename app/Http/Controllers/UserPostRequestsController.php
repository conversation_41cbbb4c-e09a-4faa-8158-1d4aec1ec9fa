<?php

namespace App\Http\Controllers;

use App\Enums\PostStatus;
use App\Enums\UserPostRequestStatus;
use App\Http\Requests\PostStoreRequest;
use App\Http\Requests\PostUpdateRequest;
use App\Http\Requests\UpdateUserPostRequestStatusRequest;
use App\Models\Post;
use App\Models\Scopes\PostPublishedScope;
use App\Models\UserPostRequest;
use App\Services\PostService;
use App\Services\PushNotificationService;
use App\Traits\RestrictFreeUser;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\ValidationException;

class UserPostRequestsController extends Controller
{
    use RestrictFreeUser;
    private PostService $postService;

    public function __construct(PostService $postService, PushNotificationService $pushNotificationService)
    {
        $this->postService = $postService;
    }

    public function index(Request $request): JsonResponse
    {
        $this->restrictFree();

        $limit = $request->limit ?? 10;
        $user_id = Auth::user()->id;
        $userPostRequests = UserPostRequest::with([
            'post' => function ($query) {
                $query->with('categories')->withCount('comments')->withoutGlobalScopes();
            }
        ])->where('user_id', $user_id)
            ->orderBy('updated_at', 'desc')
            ->paginate($limit);

        return response()->json($userPostRequests);
    }

    public function show(string $user_post_request_id): JsonResponse
    {
        $this->restrictFree();

        $userPostRequests = UserPostRequest::with([
            'post' => function ($query) {
                $query->with('categories')->withCount('comments')->withoutGlobalScopes();
            }
        ])->findOrFail($user_post_request_id);

        return response()->json($userPostRequests);
    }

    /**
     * Store a newly created resource in storage.
     * @throws \Exception
     */
    public function store(PostStoreRequest $request): JsonResponse
    {
        $this->restrictFree();

        $validatedData = $request->validated();
        $userType = 'user';

        $postData = $this->postService->createPost($validatedData, $userType);

        $userPostRequest = new UserPostRequest();
        $userPostRequest->user_id = auth()->user()->id;
        $userPostRequest->post_id = $postData['post']->id;
        $userPostRequest->save();

        return response()->json([
            'success' => true,
            'message' => $postData['message'],
            'user_post_request_data' => $userPostRequest,
            'post_data' => $postData['post'],
        ]);
    }

    /**
     * @throws ValidationException
     */
    public function update(PostUpdateRequest $request, string $user_post_request_id): JsonResponse
    {
        $this->restrictFree();

        $validatedData = $request->validated();
        $userType = 'user';
        $userPostRequest = UserPostRequest::findOrFail($user_post_request_id);
        $user = Auth::user();

        if (($userPostRequest->user_id !== $user->id) ||
            ($userPostRequest->status !== UserPostRequestStatus::Pending->value)
        ) {
            return response()->json(
                [
                    'success' => false,
                    'message' => "You are not allowed to update this post",
                ], 403);
        }

        $postData = $this->postService->updatePost($userPostRequest->post_id, $validatedData, $userType);
        $userPostRequest->updated_at = Carbon::now();
        $userPostRequest->save();

        return response()->json([
            'success' => true,
            'message' => $postData['message'],
            'user_post_request_data' => $userPostRequest,
            'post_data' => $postData['post'],
        ]);
    }

    public function destroy(string $user_post_request_id): JsonResponse
    {
        $this->restrictFree();

        $userPostRequest = UserPostRequest::findOrFail($user_post_request_id);
        $user = Auth::user();

        if (($userPostRequest->user_id !== $user->id) ||
            ($userPostRequest->status != UserPostRequestStatus::Pending->value)
        ) {
            return response()->json(
                [
                    'success' => false,
                    'message' => "You are not allowed to delete this request",
                ], 403);
        }

        $post = Post::withoutGlobalScope(PostPublishedScope::class)->findOrFail($userPostRequest->post_id);
        $post->delete();
        $userPostRequest->delete();

        return response()->json([
            'success' => true,
            'message' => 'Request deleted successfully'
        ]);
    }
}
