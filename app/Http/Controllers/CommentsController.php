<?php

namespace App\Http\Controllers;

use App\Http\Requests\CommentPatchRequest;
use App\Http\Requests\CommentPostRequest;
use App\Models\Comment;
use App\Models\Post;
use App\Services\PostService;
use App\Traits\RestrictFreeUser;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class CommentsController extends Controller
{
    use RestrictFreeUser;
    private PostService $postService;

    public function __construct(PostService $postService)
    {
        $this->postService = $postService;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(CommentPostRequest $request, string $module_id, string $post_id): JsonResponse
    {
        $this->restrictFree();

        $post = Post::findOrFail($post_id);
        $user_id = Auth::user()->id;

        $like = new Comment(['comment' => $request->comment, 'module_id' => $module_id, 'user_id' => $user_id]);
        $post->comments()->save($like);

        return response()->json(
            [
                'success' => true,
                'message' => "Comment added successful",
                'post' => $this->postService->getDetail($post_id)
            ]);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(CommentPatchRequest $request, string $module_id, string $post_id, string $comment_id)
    {
        $this->restrictFree();

        $user_id = Auth::user()->id;
        $comment = Comment::findOrFail($comment_id);

        if ($comment->user_id !== $user_id) {
            return response()->json(
                [
                    'success' => false,
                    'message' => "You are not allowed to update this comment",
                ], 403);
        }

        $comment->comment = $request->comment;
        $comment->save();

        return response()->json(
            [
                'success' => true,
                'message' => "Comment updated successful",
                'post' => $this->postService->getDetail($post_id)
            ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $module_id, string $post_id, string $comment_id)
    {
        $user_id = Auth::user()->id;
        $comment = Comment::findOrFail($comment_id);

        if ($comment->user_id !== $user_id) {
            return response()->json(
                [
                    'success' => false,
                    'message' => "You are not allowed to delete this comment",
                ], 403);
        }
        $comment->delete();

        return response()->json(
            [
                'success' => true,
                'message' => "Comment deleted successful",
                'post' => $this->postService->getDetail($post_id)
            ]);
    }
}
