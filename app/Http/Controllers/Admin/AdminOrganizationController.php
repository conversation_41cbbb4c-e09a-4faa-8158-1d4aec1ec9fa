<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Organization;
use Illuminate\Http\JsonResponse;

class AdminOrganizationController extends Controller
{
    public function index(): JsonResponse
    {
        $organizations = Organization::where('is_active', true)->get();

        return response()->json([
            'success' => 'success',
            'organizations' => $organizations,
        ]);
    }
}
