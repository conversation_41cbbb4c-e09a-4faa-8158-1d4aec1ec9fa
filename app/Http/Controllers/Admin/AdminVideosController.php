<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\VideoPostRequest;
use App\Http\Requests\VideoUpdateRequest;
use App\Models\Video;
use App\Services\VideoService;
use App\Traits\RestrictFreeUser;
use App\Traits\UploadFiles;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class AdminVideosController extends Controller
{
    use RestrictFreeUser, UploadFiles;

    private VideoService $videoService;

    public function __construct(VideoService $videoService)
    {
        $this->videoService = $videoService;
    }

    /**
     * Display a listing of the resource.
     */
    public function index(string $module_id, Request $request): JsonResponse
    {
        $limit = $request->get('limit', 10);
        $categoryId = $request->categoryId;

        $videos = Video::where('module_id', $module_id)
            ->when($categoryId, function ($query) use ($categoryId) {
                $query->where('category_id', $categoryId);
            })
            ->with('category')
            ->orderBy('id', 'DESC')
            ->paginate($limit);

        return response()->json($videos);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $module_id, string $video_id)
    {
        return $this->videoService->getDetail($video_id, $module_id);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(VideoPostRequest $request): JsonResponse
    {
        $data = $request->only([
            'title',
            'tag_id',
            'video_type',
            'video_link',
            'category_id',
            'module_id',
        ]);

        if ($request->hasFile('thumbnail')) {
            $data['thumbnail'] = $this->uploadFileToS3(
                $request->file('thumbnail'),
                'video_photos',
                $request->title . '-thumbnail'
            );
        }
        if ($request->hasFile('video_file')) {
            $data['video_link'] = $this->uploadFileToS3(
                $request->file('video_file'),
                'video_videos',
                $request->title . '-video'
            );
        }

        $video_data = Video::create(
            $data
        )->toArray();

        return response()->json(
            [
                'success' => true,
                'message' => 'Video added successfully',
                'video_data' => $video_data,
            ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(VideoUpdateRequest $request, string $module_id, string $video_id)
    {
        $video = Video::findOrFail($video_id);

        $video->title = $request->title;
        if ($request->video_link) {
            $video->video_link = $request->video_link;
        }
        $video->video_type = $request->video_type;
        $video->category_id = $request->category_id;

        if ($request->hasFile('thumbnail')) {
            $video->thumbnail = $this->uploadFileToS3(
                $request->file('thumbnail'),
                'video_photos',
                $request->title . '-thumbnail'
            );
        }
        if ($request->hasFile('video_file')) {
            $video->video_link = $this->uploadFileToS3(
                $request->file('video_file'),
                'video_videos',
                $request->title . '-video'
            );
        }

        $video->save();

        return response()->json(
            [
                'success' => true,
                'message' => "Video updated successful",
            ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $module_id, string $video_id)
    {
        $video = Video::findOrFail($video_id);

        $video->delete();

        return response()->json(
            [
                'success' => true,
                'message' => "Video deleted successful",
            ]);
    }
}
