<?php

namespace App\Http\Controllers\Admin;

use App\Enums\PostStatus;
use App\Enums\UserPostRequestStatus;
use App\Http\Controllers\Controller;
use App\Http\Requests\UpdateUserPostRequestStatusRequest;
use App\Models\Post;
use App\Models\Scopes\PostPublishedScope;
use App\Models\UserPostRequest;
use App\Services\PostService;
use App\Services\PushNotificationService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class AdminUserPostRequestsController extends Controller
{
    private PostService $postService;

    public function __construct(PostService $postService, PushNotificationService $pushNotificationService)
    {
        $this->postService = $postService;
    }

    public function index(Request $request): JsonResponse
    {
        $limit = $request->limit ?? 10;
        $userId = $request->userId;
        $status = $request->status;
        $moduleId = $request->moduleId;

        $userPostRequestsQuery = UserPostRequest::with('user');

        if (!empty($userId)) {
            $userPostRequestsQuery->where('user_id', $userId);
        }

        if ($request->exists('status') && $status !== null) {
            $userPostRequestsQuery->where('status', $status);
        }

        if (!empty($moduleId)) {
            $userPostRequestsQuery = $userPostRequestsQuery->whereHas(
                'post', function ($query) use ($moduleId) {
                if (!empty($moduleId)) {
                    $query->where('module_id', $moduleId);
                }
                $query->withoutGlobalScopes();
            }
            );
        }

        $userPostRequests = $userPostRequestsQuery->with([
            'post' => function ($query) {
                $query->withoutGlobalScopes();
            }
        ])->orderBy('updated_at', 'desc')
            ->paginate($limit);

        return response()->json($userPostRequests);
    }

    public function show(string $user_post_request_id): JsonResponse
    {
        $userPostRequests = UserPostRequest::with([
            'post' => function ($query) {
                $query->with('categories')->withCount('comments')->withoutGlobalScopes();
            }
        ])->findOrFail($user_post_request_id);

        return response()->json($userPostRequests);
    }

    public function updateStatus(string $user_post_request_id, UpdateUserPostRequestStatusRequest $request): JsonResponse
    {
        $userPostRequest = UserPostRequest::findOrFail($user_post_request_id);
        $post = Post::withoutGlobalScope(PostPublishedScope::class)->findOrFail($userPostRequest->post_id);

        if ($request->status == UserPostRequestStatus::Accepted->value) {
            $post->status = PostStatus::Published;
            $this->postService->sendNewPostNotification($post);
        } else {
            $post->status = PostStatus::Rejected;
        }
        $post->save();

        $userPostRequest->status = $request->status;
        $userPostRequest->reason = $request->reason;
        $userPostRequest->save();

        return response()->json(
            [
                'success' => true,
                'message' => 'Status updated successfully',
            ]);
    }
}
