<?php

namespace App\Http\Controllers\Admin;

use App\Enums\BroadcastMessageType;
use App\Http\Controllers\Controller;
use App\Http\Requests\AdminBroadcastMessageAddRequest;
use App\Jobs\BroadcastMessageJob;
use App\Models\AdminBroadcastMessage;
use App\Models\FcmTokenList;
use App\Models\FCMTokenListUser;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class AdminBroadcastMessageController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $limit = $request->limit ?? 10;
        $broadcasts = AdminBroadcastMessage::with('admin')
            ->orderBy('created_at', 'desc')
            ->paginate($limit);

        return response()->json($broadcasts);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(AdminBroadcastMessageAddRequest $request)
    {
        if ($request->type === BroadcastMessageType::TOPIC->value) {
            $list = FcmTokenList::findOrFail($request->type_value);
            $listExistsWithUsers = FCMTokenListUser::where('fcm_token_list_id', $list->id)->first();

            if (!$listExistsWithUsers) {
                return response()->json([
                    'success' => false,
                    'message' => 'Broadcast topic does not have any users attached to it.'
                ], 400);
            }
        }

        $broadcast = AdminBroadcastMessage::create([
            'type' => BroadcastMessageType::from($request->type),
            'type_value' => is_array($request->type_value)
                ? json_encode($request->type_value)
                : $request->type_value,
            'title' => $request->title,
            'body' => $request->body,
            'url' => $request->url,
            'admin_id' => Auth::id(),
        ]);

        BroadcastMessageJob::dispatch($broadcast);

        return response()->json([
            'success' => true,
            'message' => 'Broadcast message created and scheduled successfully.',
            'data' => $broadcast], 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(AdminBroadcastMessage $adminBroadcastMessage)
    {
        //
    }
}
