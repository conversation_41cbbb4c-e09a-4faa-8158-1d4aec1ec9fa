<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\AdminBroadcastTopicAddRequest;
use App\Http\Requests\AdminBroadcastTopicUserSyncRequest;
use App\Models\FcmTokenList;
use App\Services\PushNotificationService;
use Illuminate\Support\Str;

class AdminBroadcastTopicController extends Controller
{
    public function __construct(protected PushNotificationService $pushNotificationService)
    {
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $broadcastList = FcmTokenList::orderBy('created_at', 'desc')
            ->get();

        return response()->json([
            'success' => true,
            'broadcast_topics' => $broadcastList
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(AdminBroadcastTopicAddRequest $request)
    {
        $list = FcmTokenList::create([
            'name' => Str::slug($request->name),
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Broadcast topic created successfully.',
            'data' => $list], 201);
    }

    public function sync(AdminBroadcastTopicUserSyncRequest $request, string $fcmTokenListId)
    {
        $this->pushNotificationService->syncTopicTokens($fcmTokenListId, $request->user_ids);

        return response()->json([
            'success' => true,
            'message' => 'Users attached successfully',
        ]);
    }

    public function show(string $fcmTokenListId)
    {
        $fcmTokenList = FcmTokenList::with('users')->findOrFail($fcmTokenListId);

        return response()->json([
            'success' => true,
            'broadcast_topic' => $fcmTokenList
        ]);
    }
}
