<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\MusicPostRequest;
use App\Http\Requests\MusicUpdateRequest;
use App\Models\Music;
use App\Services\MusicService;
use App\Traits\UploadFiles;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class AdminMusicController extends Controller
{
    use UploadFiles;

    private MusicService $musicService;

    public function __construct(MusicService $musicService)
    {
        $this->musicService = $musicService;
    }

    /**
     * Display a listing of the resource.
     */
    public function index(string $module_id, Request $request): JsonResponse
    {
        $limit = $request->get('limit', 10);
        $categoryId = $request->categoryId;

        $music = Music::with(['category', 'tag'])
            ->when($categoryId, function ($query) use ($categoryId) {
                $query->where('category_id', $categoryId);
            })
            ->orderBy('id', 'desc')
            ->paginate($limit);

        return response()->json($music);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $module_id, string $music_id)
    {
        return $this->musicService->getDetail($music_id, $module_id);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(MusicPostRequest $request): JsonResponse
    {
        $data = $request->only([
            'title',
            'tag_id',
            'category_id',
            'module_id',
        ]);

        if ($request->hasFile('thumbnail')) {
            $data['thumbnail'] = $this->uploadFileToS3(
                $request->file('thumbnail'),
                'music_photos',
                $request->title . '-thumbnail'
            );
        }
        if ($request->hasFile('audio_file')) {
            $data['audio_link'] = $this->uploadFileToS3(
                $request->file('audio_file'),
                'music_audios',
                $request->title . '-audio'
            );
        }

        $music_data = Music::create(
            $data
        )->toArray();

        return response()->json(
            [
                'success' => true,
                'message' => 'Music added successfully'
            ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(MusicUpdateRequest $request, string $module_id, string $music_id)
    {
        $music = Music::findOrFail($music_id);

        $music->title = $request->title;
        $music->category_id = $request->category_id;

        if ($request->hasFile('thumbnail')) {
            $music->thumbnail = $this->uploadFileToS3(
                $request->file('thumbnail'),
                'music_photos',
                $request->title . '-thumbnail'
            );
        }
        if ($request->hasFile('audio_file')) {
            $music->audio_link = $this->uploadFileToS3(
                $request->file('audio_file'),
                'music_audios',
                $request->title . '-audio'
            );
        }

        $music->save();

        return response()->json(
            [
                'success' => true,
                'message' => "Music updated successful",
                'music_data' => $this->musicService->getDetail($music->id, $music->module_id),
            ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $module_id,string $music_id)
    {
        $music = Music::findOrFail($music_id);

        $music->delete();

        return response()->json(
            [
                'success' => true,
                'message' => "Music deleted successful",
            ]);
    }
}
