<?php

namespace App\Http\Controllers\Admin;

use App\Enums\PostStatus;
use App\Http\Controllers\Controller;
use App\Http\Requests\PostStoreRequest;
use App\Http\Requests\PostUpdateRequest;
use App\Models\Category;
use App\Models\Module;
use App\Models\Post;
use App\Services\PostService;
use App\Services\PushNotificationService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;

class AdminPostsController extends Controller
{
    private PostService $postService;
    protected PushNotificationService $pushNotificationService;

    public function __construct(PostService $postService, PushNotificationService $pushNotificationService)
    {
        $this->postService = $postService;
        $this->pushNotificationService = $pushNotificationService;
    }

    /**
     * Display a listing of the resource.
     */
    public function index(string $module_id, Request $request): JsonResponse
    {
        $limit = $request->get('limit', 10);
        $categoryId = $request->categoryId;

        $posts = Post::where('module_id', $module_id)
            ->with('categories')
            ->when($categoryId, function ($query) use ($categoryId) {
                $query->whereHas('categories', function ($q) use ($categoryId) {
                    $q->where('category_id', $categoryId);
                });
            })
            ->orderBy('id', 'DESC')
            ->paginate($limit);

        return response()->json($posts);
    }

    /**
     * Store a newly created resource in storage.
     * @throws ValidationException|\Exception
     */
    public function store(string $module_id, PostStoreRequest $request): JsonResponse
    {
        $validatedData = $request->validated();
        $userType = 'admin';

        $postData = $this->postService->createPost($validatedData, $userType);

        return response()->json([
            'success' => true,
            'message' => $postData['message'],
            'post_data' => $this->postService->getDetail($postData['post']->id),
        ]);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $module_id, string $post_id): JsonResponse
    {
        $post = Post::with('likes')
            ->with('categories')
            ->with('comments.user')
            ->findOrFail($post_id);

        return response()->json(
            [
                'success' => true,
                'data' => $post,
            ]);
    }

    /**
     * @throws ValidationException
     */
    public function update(PostUpdateRequest $request, string $module_id, string $post_id): JsonResponse
    {
        $validatedData = $request->validated();
        $userType = auth('admin')->check() ? 'admin' : 'user';

        $postData = $this->postService->updatePost($post_id, $validatedData, $userType);

        return response()->json([
            'success' => true,
            'message' => $postData['message'],
            'post_data' => $this->postService->getDetail($postData['post']->id),
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $module_id, string $post_id): JsonResponse
    {
        $post = Post::findOrFail($post_id);

        $post->delete();

        return response()->json(
            [
                'success' => true,
                'message' => "Post deleted successfully",
            ]);
    }
}
