<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\AdminChangeUserPasswordRequest;
use App\Http\Requests\AdminUserGetRequest;
use App\Http\Requests\UserSearchRequest;
use App\Mail\AdminUserPasswordChange;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Mail;

class AdminUsersController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(AdminUserGetRequest $request)
    {
        $limit = $request->limit ?? 10;
        $isVerified = $request->isVerified;
        $userType = $request->userType;
        $userMembershipType = $request->userMembershipType;
        $organizationId = $request->organizationId;
        $referralId = $request->referralId;
        $userId = $request->userId;

        $usersQuery = User::with('organization');

        if (isset($isVerified)) {
            $isVerified
                ? $usersQuery->whereNotNull('email_verified_at')
                : $usersQuery->whereNull('email_verified_at');
        }

        if (!empty($userType)) {
            $usersQuery->where('user_type', $userType);
        }

        if (!empty($userMembershipType)) {
            $usersQuery->where('user_membership_type', $userMembershipType);
        }

        if (!empty($organizationId)) {
            $usersQuery->where('organization_id', $organizationId);
        }

        if (!empty($referralId)) {
            $usersQuery->where('referral_id', $referralId);
        }

        if (!empty($userId)) {
            $usersQuery->where('id', $userId);
        }

        $users = $usersQuery->orderByDesc('id')->paginate($limit);
        $users->getCollection()->transform(function ($user) {
            return $user->makeVisible(['created_at', 'email_verified_at']);
        });

        return response()->json($users);
    }

    public function show(string $userId): JsonResponse
    {
        $user = User::with('organization')->findOrFail($userId);
        $user->makeVisible(['created_at', 'email_verified_at']);

        return response()->json($user);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function search(UserSearchRequest $request): JsonResponse
    {
        $limit = $request->limit ?? 10;

        $searchValues = explode(" ", $request->search_value);
        $firstName = $searchValues[0];
        $lastName = $searchValues[1] ?? $searchValues[0];

        $users = User::where(function ($query) use ($firstName, $lastName) {
            $query->where('first_name', 'like', "%" . $firstName . "%")
                ->orWhere('last_name', 'like', "%" . $lastName . "%")
                ->orWhere('email', 'like', "%" . $lastName . "%");
        })->paginate($limit);

        return response()->json($users);
    }

    public function changePassword(AdminChangeUserPasswordRequest $request, string $userId)
    {
        $user = User::findOrFail($userId);

        $user->password = bcrypt($request->new_password);
        $user->save();

        Mail::to($user)->send(new AdminUserPasswordChange($user, $request->new_password));

        return response()->json(['success' => true, 'message' => 'Password changed successfully']);
    }
}
