<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\CategoryPostRequest;
use App\Models\Category;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Str;

class AdminCategoriesController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(): JsonResponse
    {
        $categories = Category::all();

        return response()->json(
            [
                'success' => true,
                'categories' => $categories,
            ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(CategoryPostRequest $request): JsonResponse
    {

        $category = new Category();

        $category->name = $request->name;
        $category->slug = Str::slug($request->name);
        $category->module_id = $request->module_id;
        $category->layout = $request->layout;
        $category->description = $request->description;
        $category->save();

        return response()->json(
            [
                'success' => true,
                'message' => 'Category added successfully',
                'category_data' => $category,
            ]);
    }

    /**
     * Update a newly created resource in storage.
     */
    public function update(CategoryPostRequest $request, string $id): JsonResponse
    {
        $category = Category::findOrFail($id);

        $category->name = $request->name;
        $category->slug = Str::slug($request->name);
        $category->layout = $request->layout;
        $category->description = $request->description;
        $category->save();

        return response()->json(
            [
                'success' => true,
                'message' => 'Category updated successfully',
                'category_data' => $category,
            ]);
    }
}
