<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ReferralCode;
use App\Models\User;
use App\Services\ReferralCodeService;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class AdminReferralCodeController extends Controller
{

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $referralCodes = ReferralCode::where('is_active', true)
            ->with('user')
            ->whereHas('user')
            ->orderBy('id', 'DESC')
            ->get();

        return response()->json([
            'success' => true,
            'referral_codes' => $referralCodes,
        ]);
    }
}
