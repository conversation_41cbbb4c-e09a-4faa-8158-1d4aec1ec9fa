<?php

namespace App\Http\Controllers;

use App\Http\Requests\CommentPatchRequest;
use App\Http\Requests\CommentPostRequest;
use App\Models\Comment;
use App\Models\PodCast;
use App\Services\PodCastService;
use App\Traits\RestrictFreeUser;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class PodCastCommentsController extends Controller
{
    use RestrictFreeUser;
    private PodCastService $podCastService;

    public function __construct(PodCastService $podCastService)
    {
        $this->podCastService = $podCastService;
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(CommentPostRequest $request, string $module_id, string $podcast_id): JsonResponse
    {
        $this->restrictFree();

        $podcast = PodCast::findOrFail($podcast_id);
        $user_id = Auth::user()->id;

        $comment = new Comment(['comment' => $request->comment, 'module_id' => $module_id, 'user_id' => $user_id]);
        $podcast->comments()->save($comment);

        return response()->json(
            [
                'success' => true,
                'message' => "Comment added successful",
                'podcast' => $this->podCastService->getDetail($podcast_id, $module_id),
            ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(CommentPatchRequest $request, string $module_id, string $podcast_id, string $comment_id): JsonResponse
    {
        $this->restrictFree();

        $user_id = Auth::user()->id;
        $comment = Comment::findOrFail($comment_id);

        if ($comment->user_id !== $user_id) {
            return response()->json(
                [
                    'success' => false,
                    'message' => "You are not allowed to update this comment",
                ], 403);
        }

        $comment->comment = $request->comment;
        $comment->save();

        return response()->json(
            [
                'success' => true,
                'message' => "Comment updated successful",
                'podcast' => $this->podCastService->getDetail($podcast_id, $module_id),
            ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $module_id, string $podcast_id, string $comment_id): JsonResponse
    {
        $user_id = Auth::user()->id;
        $comment = Comment::findOrFail($comment_id);

        if ($comment->user_id !== $user_id) {
            return response()->json(
                [
                    'success' => false,
                    'message' => "You are not allowed to delete this comment",
                ], 403);
        }
        $comment->delete();

        return response()->json(
            [
                'success' => true,
                'message' => "Comment deleted successful",
                'podcast' => $this->podCastService->getDetail($podcast_id, $module_id),
            ]);
    }
}
