<?php

namespace App\Http\Controllers;

use App\Enums\UserMembershipType;
use App\Enums\UserType;
use App\Http\Requests\AffiliateCodePostRequest;
use App\Http\Requests\AffiliateCodePutRequest;
use App\Models\AffiliateCode;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Str;

class AffiliateCodesController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(): JsonResponse
    {
        $affiliateCodes = AffiliateCode::with('redeemedBy')
            ->orderBy('id', 'desc')
            ->get();

        return response()->json([
            'success' => true,
            'affiliate_codes' => $affiliateCodes,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(AffiliateCodePostRequest $request)
    {
        $affiliateCode = AffiliateCode::create([
            'code' => str_replace(' ', '', Str::upper(Str::squish($request->code))),
            'created_by' => auth()->id(),
        ]);

        return response()->json([
            'success' => true,
            'affiliate_code' => $affiliateCode,
        ], 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $affiliateCode = AffiliateCode::with('redeemedBy')->findOrFail($id);

        return response()->json([
            'success' => 'success',
            'affiliate_code' => $affiliateCode,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(AffiliateCodePutRequest $request, string $id)
    {
        $affiliateCode = AffiliateCode::findOrFail($id);

        if ($affiliateCode->redeemed_by) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot update an already redeemed affiliate code.',
            ], 403);
        }

        $affiliateCode->update([
            'code' => str_replace(' ', '', Str::upper(Str::squish($request->code))),
            'is_active' => $request->is_active,
            'created_by' => auth()->id(),
        ], ['id' => $id]);

        return response()->json([
            'success' => true,
            'affiliate_code' => $affiliateCode,
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $affiliateCode = AffiliateCode::findOrFail($id);

        if ($affiliateCode->redeemed_by) {
            $user = User::findOrFail($affiliateCode->redeemed_by);

            if ($user->user_membership_type === UserMembershipType::AFFILIATE_CDOE->value) {
                $user->user_membership_type = null;
                $user->user_type = UserType::FREE->value;
                $user->save();
            }

        }
        $affiliateCode->delete();

        return response()->json([
            'success' => true,
            'message' => 'Affiliate code deleted successfully.',
        ]);
    }
}
