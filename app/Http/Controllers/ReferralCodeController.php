<?php

namespace App\Http\Controllers;

use App\Models\ReferralCode;
use App\Models\User;
use App\Services\ReferralCodeService;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class ReferralCodeController extends Controller
{
    private ReferralCodeService $referralCodeService;

    public function __construct(ReferralCodeService $referralCodeService)
    {
        $this->referralCodeService = $referralCodeService;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $referralCodes = ReferralCode::where('is_active', true)
            ->orderBy('id', 'DESC')
            ->get();

        return response()->json([
            'success' => true,
            'referral_codes' => $referralCodes,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $user = $request->user();

        $referralCode = ReferralCode::where('user_id', $user->id)->where('is_active', true)->first();

        if (!$referralCode) {
            $referralCode = new ReferralCode();
            $referralCode->user_id = $user->id;
            $referralCode->code = $this->referralCodeService->generateReferralCode($user);
            $referralCode->save();
        }

        return response()->json([
            'success' => true,
            'referral_code' => $referralCode->code,
        ]);
    }

    /**
     * Display the specified resource.
     */
    public function show(ReferralCode $referralCode)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, ReferralCode $referralCode)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ReferralCode $referralCode)
    {
        //
    }
}
