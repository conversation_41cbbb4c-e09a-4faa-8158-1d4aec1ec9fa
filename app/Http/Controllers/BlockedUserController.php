<?php

namespace App\Http\Controllers;

use App\Http\Requests\BlockUserRequest;
use App\Http\Requests\UnblockUserRequest;
use App\Models\BlockedUser;
use App\Models\UserFollower;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class BlockedUserController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $user = Auth::user();

        $blockedUsers = BlockedUser::where('blocked_by', $user->id)->with('user')->get();

        return response()->json(
            [
                'success' => true,
                'blocked_users' => $blockedUsers
            ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(BlockUserRequest $request)
    {
        $user = Auth::user();
        $blockedUser = new BlockedUser();
        $blockedUser->user_id = $request->user_id;
        $blockedUser->blocked_by = $user->id;
        $blockedUser->save();

        UserFollower::where('follower_id', $user->id)
            ->where('user_id', $request->user_id)
            ->delete();

        UserFollower::where('user_id', $user->id)
            ->where('follower_id', $request->user_id)
            ->delete();

        return response()->json(
            [
                'success' => true,
                'message' => "User blocked successfully."
            ]);
    }

    /**
     * Display the specified resource.
     */
    public function show(BlockedUser $blockedUser)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, BlockedUser $blockedUser)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(UnblockUserRequest $request)
    {
        $blockedUser = BlockedUser::where('user_id', $request->user_id)->firstOrFail();
        $blockedUser->delete();

        return response()->json(
            [
                'success' => true,
                'message' => "User unblocked successfully."
            ]);
    }
}
