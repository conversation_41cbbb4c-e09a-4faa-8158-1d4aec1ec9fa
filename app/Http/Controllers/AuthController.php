<?php

namespace App\Http\Controllers;

use App\Enums\UserType;
use App\Http\Requests\RegisterUserRequest;
use App\Http\Requests\UserVerificationRequest;
use App\Jobs\NewUserJob;
use App\Jobs\StudentPostVerificationJob;
use App\Models\InviteUser;
use App\Models\User;
use App\Services\AffiliateCodeService;
use App\Services\OrganizationService;
use App\Services\OTPService;
use App\Services\PushNotificationService;
use App\Services\ReferralCodeService;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class AuthController extends Controller
{
    private OTPService $OtpService;
    private PushNotificationService $pushNotificationService;
    private AffiliateCodeService $affiliateCodeService;
    private ReferralCodeService $referralCodeService;

    public function __construct(OTPService $OtpService, PushNotificationService $pushNotificationService, AffiliateCodeService $affiliateCodeService, ReferralCodeService $referralCodeService)
    {
        $this->OtpService = $OtpService;
        $this->pushNotificationService = $pushNotificationService;
        $this->affiliateCodeService = $affiliateCodeService;
        $this->referralCodeService = $referralCodeService;
    }

    /**
     * @OA\Post(
     * path="/register",
     * operationId="registerUser",
     * tags={"Auth"},
     * summary="User Registration",
     * description="User Registration",
     * @OA\RequestBody(
     *          required=true,
     *          @OA\JsonContent(
     *             required={"first_name", "last_name", "password", "email"},
     *             @OA\Property(property="first_name", type="string", format="string", example="Ansar"),
     *             @OA\Property(property="last_name", type="string", format="string", example="Khalil"),
     *             @OA\Property(property="password", type="password", format="string", example="ansar@786"),
     *             @OA\Property(property="email", type="email", format="string", example="<EMAIL>"),
     *          ),
     *       ),
     * @OA\Response(
     * response=200,
     * description="Example of Success response",
     * @OA\JsonContent(
     * @OA\Property(property="success", type="booleon", example="true"),
     * @OA\Property(property="message", type="string", example="User registered successfully. Please check your mailbox to verify email address."),
     * @OA\Property(property="user", type="object", example="{'first_name': 'Ansar','last_name': 'Khalil','email': '<EMAIL>','id': 2  }"),
     * )
     * ),
     * @OA\Response(
     * response=422,
     * description="Example of Bad Request",
     * @OA\JsonContent(
     * @OA\Property(property="errors", type="array",
     *               @OA\Items(
     *                   @OA\Property(property="email",type="string",example="The email has already been taken.")
     *               )
     *  ),
     * )
     * )
     * )
     *
     * )
     *
     */
    public function register(RegisterUserRequest $request): JsonResponse
    {
        $orgService = new OrganizationService();
        $user = new User();
        $user->first_name = $request->first_name;
        $user->last_name = $request->last_name;
        $user->user_name = $request->user_name;
        $user->email = trim($request->email);
        $user->organization_id = $request->organization_id;
        $user->affiliate_code = $request->affiliate_code;
        $user->is_student_fe_check = $request->is_student;
        $user->password = bcrypt($request->password);
        //$user->is_student = $this->isStudentEmail($user->email);
        $user->is_student = $orgService->makeStudentViaOrg($user->organization_id);
        $user->referral_id = $this->referralCodeService->handleReferralCode($request->referral_code);
        $user->save();

        $this->affiliateCodeService->handleAffiliateCode($user);

        $otp = $this->OtpService->createOTP($user);

        NewUserJob::dispatch($user, $otp);

        return response()->json(
            [
                'success' => true,
                'message' => 'User registered successfully. Please check your mailbox to verify email address.',
                'user' => $user,
            ]
        );
    }

    protected function isStudentEmail(string $email): bool
    {
        $domain = substr(strrchr($email, "@"), 1);

        return str_contains($domain, '.edu');
    }

    /**
     * @OA\Post(
     * path="/verify",
     * operationId="verifyUserEmail",
     * tags={"Auth"},
     * summary="Verify user email",
     * description="Verify user email",
     * @OA\RequestBody(
     *          required=true,
     *          @OA\JsonContent(
     *             required={ "password", "email"},
     *             @OA\Property(property="otp", type="string", format="string", example="3506"),
     *             @OA\Property(property="email", type="email", format="string", example="<EMAIL>"),
     *          ),
     *       ),
     * @OA\Response(
     * response=200,
     * description="Example of Success response",
     * @OA\JsonContent(
     * @OA\Property(property="success", type="booleon", example="true"),
     * @OA\Property(property="message", type="string", example="User registered successfully. Please check your mailbox to verify email address."),
     * @OA\Property(property="user", type="object", example="{'first_name': 'Ansar','last_name': 'Khalil','email': '<EMAIL>','id': 2  }"),
     * )
     * ),
     * @OA\Response(
     * response=401,
     * description="Example of Bad Request",
     * @OA\JsonContent(
     * @OA\Property(property="success", type="booleon", example="false"),
     *  @OA\Property(property="message", type="string", example="OTP is incorrect"),
     * )
     * )
     * )
     *
     * )
     *
     */
    public function verify(UserVerificationRequest $request): JsonResponse
    {
        $user = User::where('email', $request->email)->firstOrFail();

        if ($this->OtpService->verifyOTP($user, $request->otp)) {

            InviteUser::where('email', $request->email)->update(['is_registered' => true]);

            $token = $user->createToken($user->first_name, ['role:user']);

            $user->email_verified_at = Carbon::now();
            $user->save();

            if($user->is_student){
                StudentPostVerificationJob::dispatch($user);
            }

            return response()->json(
                [
                    'success' => true,
                    'message' => 'User verified successfully.',
                    'token' => $token->plainTextToken,
                    'user' => $user
                ]
            );
        }

        return response()->json(['success' => false, 'message' => 'OTP is incorrect.']);
    }

    /**
     * @OA\Post(
     * path="/login",
     * operationId="loginUser",
     * tags={"Auth"},
     * summary="User Login",
     * description="User Login",
     * @OA\RequestBody(
     *          required=true,
     *          @OA\JsonContent(
     *             required={ "password", "email"},
     *             @OA\Property(property="password", type="password", format="string", example="ansar@786"),
     *             @OA\Property(property="email", type="email", format="string", example="<EMAIL>"),
     *          ),
     *       ),
     * @OA\Response(
     * response=200,
     * description="Example of Success response",
     * @OA\JsonContent(
     * @OA\Property(property="success", type="booleon", example="true"),
     * @OA\Property(property="message", type="string", example="User registered successfully. Please check your mailbox to verify email address."),
     * @OA\Property(property="user", type="object", example="{'first_name': 'Ansar','last_name': 'Khalil','email': '<EMAIL>','id': 2  }"),
     * )
     * ),
     * @OA\Response(
     * response=401,
     * description="Example of Bad Request",
     * @OA\JsonContent(
     * @OA\Property(property="success", type="booleon", example="false"),
     *  @OA\Property(property="is_verified", type="booleon", example="false"),
     * @OA\Property(property="message", type="string", example="v"),
     * )
     * )
     * )
     *
     * )
     *
     */
    public function login(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email|max:60',
            'password' => 'required|max:60',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }


        if (Auth::attempt($request->only('email', 'password'))) {
            $user = User::where('email', $request->email)->firstOrFail();
            if ($user->email_verified_at) {

                if ($request->client_platform === 'mobile' && $user->user_type !== UserType::PREMIUM->value) {
                    return response()->json(
                        [
                            'success' => false,
                            'is_verified' => true,
                            'message' => 'You are not authorized. Please complete your payment on Akina Web to gain access to Akina Connect.',
                        ], 403
                    );
                }

                $token = $user->createToken($user->first_name, ['role:user']);

                return response()->json(
                    [
                        'success' => true,
                        'is_verified' => true,
                        'message' => 'User logged in successfully.',
                        'token' => $token->plainTextToken,
                        'user' => $user
                    ]
                );
            } else {

                if ($request->client_platform === 'mobile' && $user->user_type !== UserType::PREMIUM->value) {
                    return response()->json(
                        [
                            'success' => false,
                            'is_verified' => true,
                            'message' => 'You are not authorized. Please complete your payment on Akina Web to gain access to Akina Connect.',
                        ], 403
                    );
                }

                $this->OtpService->createAndSendOTP($user);

                return response()->json(
                    [
                        'success' => true,
                        'is_verified' => false,
                        'message' => 'Please verify your email address before proceeding further. A new OTP has been sent to your email.'
                    ], 401
                );
            }
        }

        return response()->json(
            [
                'success' => false,
                'is_verified' => false,
                'message' => 'Invalid credentials.'
            ], 401);
    }

    public function registerFromWP(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'first_name' => 'required',
            'email' => 'required|email|unique:users,email',
            'password' => 'required',
            'affiliate_code' => 'string',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $user = new User();
        $user->first_name = $request->first_name;
        $user->last_name = $request->last_name ?? "";
        $user->email = $request->email;
        $user->affiliate_code = $request->affiliate_code;
        $user->type = 'wordpress';
        $user->password = bcrypt($request->password);
        $user->save();

        return response()->json(
            [
                'success' => true,
                'message' => 'User registered successfully. ',
                'user' => $user,
            ]
        );
    }

    public function activateWpUser(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email|exists:users,email',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $user = User::where('email', $request->email)->firstOrFail();
        $user->email_verified_at = Carbon::now();
        $user->save();

        return response()->json(
            [
                'success' => true,
                'message' => 'User activated successfully. ',
                'user' => $user,
            ]
        );
    }

    public function verifyUserExternally(Request $request): JsonResponse
    {
        $token = $request->bearerToken();

        if (!$token) {
            return response()->json(['valid' => false], 400);
        }

        $tokenParts = explode('|', $token);
        $plainToken = $tokenParts[1] ?? null;
        $hashedToken = hash('sha256', $plainToken);

        $cacheKey = 'valid_token:' . $token;

        $userTokenCache =  Cache::get($cacheKey);
         if ($userTokenCache) {
             return response()->json(['valid' => !!$userTokenCache, 'user_id' => $userTokenCache->tokenable_id ?? null]);
         }

        $userToken = DB::table('personal_access_tokens')
            ->where('token', $hashedToken)
            ->select('tokenable_id', 'tokenable_type')
            ->first();

        if ($userToken && $userToken->tokenable_type === User::class) {
            Cache::put($cacheKey, $userToken, now()->addMinutes(30));
        }

        return response()->json(['valid' => !!$userToken, 'user_id' => $userToken->tokenable_id ?? null]);
    }

    public function logOut(Request $request): JsonResponse
    {
        $platform = $request->client_platform_os;

        $is_token_deactivated = $this->pushNotificationService->deactivateToken($request->user(), $platform);
        $request->user()->currentAccessToken()->delete();

        return response()->json(['success' => true, 'message' => 'User logged out successfully.', 'is_token_deactivated' => $is_token_deactivated]);
    }
}
