<?php

namespace App\Http\Controllers;

use App\Http\Requests\SupportStoreRequest;
use App\Mail\SupportForm;
use App\Models\ConnectWithUsForm;
use App\Models\UserSupport;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;

class ConnectWithUsFormController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $connectWithUs = ConnectWithUsForm::orderBy('id', 'ASC')->get();

        return response()->json(
            [
                'success' => true,
                'form_data' => $connectWithUs,
            ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(SupportStoreRequest $request): \Illuminate\Http\JsonResponse
    {
        $data = $request->only([
            'first_name',
            'last_name',
            'email',
            'phone_number',
            'topic',
            'message',
        ]);

        $connectWithUs = ConnectWithUsForm::create(
            $data
        );


        Mail::to('<EMAIL>')->send(new \App\Mail\ConnectWithUsForm($connectWithUs['email'], $connectWithUs['first_name'], $connectWithUs['last_name'], $connectWithUs['phone_number'], $connectWithUs['topic'], $connectWithUs['message']));

        return response()->json(
            [
                'success' => true,
                'message' => 'Your request is submitted successfully',
                'form_data' => $connectWithUs,
            ]);
    }
}
