<?php

namespace App\Http\Controllers;

use App\Models\Page;

class PagesController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $pages = Page::all();

        return response()->json(
            [
                'success' => true,
                'pages' => $pages,
            ]);
    }

    /**
     * Display a listing of the resource.
     */
    public function show(string $slug)
    {
        $page = Page::with(['sections.details'])->where('slug', $slug)->first();

        return response()->json(
            [
                'success' => true,
                'page' => $page,
            ]);
    }

}
