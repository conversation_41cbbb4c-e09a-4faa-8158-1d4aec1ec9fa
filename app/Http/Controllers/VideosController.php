<?php

namespace App\Http\Controllers;

use App\Models\BookMark;
use App\Models\Category;
use App\Models\Like;
use App\Models\Module;
use App\Models\Tag;
use App\Models\Video;
use App\Services\VideoService;
use App\Traits\RestrictFreeUser;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class VideosController extends Controller
{
    use RestrictFreeUser;
    private VideoService $videoService;

    public function __construct(VideoService $videoService)
    {
        $this->videoService = $videoService;
    }

    /**
     * Display a listing of the resource.
     */
    public function index(string $module_id, Request $request): JsonResponse
    {
        $isWeb = Str::contains($request->route()->uri, 'admin');

        $module = Module::where('type', 'video')->where('id', $module_id)->firstOrFail();

        $responseArr = ['success' => true, 'module' => $module];
        if ($isWeb) {
            $responseArr['videos'] = Video::where('module_id', $module_id)->with('category')->orderBy('id', 'DESC')->get();
        } else {
            $responseArr['tags'] = Tag::where('module_id', $module_id)->with('videos')->whereHas('videos')->get();
            $responseArr['categories'] = Category::where('module_id', $module_id)->with('videos', function ($query) {
                $query->orderBy('id', 'asc');
            })->whereHas('videos')->get();
        }

        return response()->json($responseArr);
    }

    public function getByCategory(string $module_id, string $category_id, Request $request): JsonResponse
    {
        $category = Category::findOrFail($category_id);

        $videos = Video::where('category_id', $category_id)
                        ->where('module_id', $module_id)
                        ->orderBy('id', 'desc')
                        ->get();

        return response()->json(
            [
                'success' => true,
                'category' => $category,
                'videos' => $videos,
            ]);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $module_id, string $video_id)
    {
        return $this->videoService->getDetail($video_id, $module_id);
    }

    public function like(Request $request, string $module_id, string $video_id): JsonResponse
    {
        $this->restrictFree();

        $video = Video::findOrFail($video_id);
        $user_id = Auth::user()->id;

        if ($is_liked = $video->likes()->where('user_id', $user_id)->first()) {
            $is_liked->delete();
            $status = 'unlike';
        } else {
            $like = new Like(['module_id' => $module_id, 'user_id' => $user_id]);
            $video->likes()->save($like);
            $status = 'like';
        }

        return response()->json(
            [
                'success' => true,
                'message' => "Video $status successful",
                'status' => $status,
                'video' => $this->videoService->getDetail($video_id, $module_id),
            ]);
    }

    public function bookMark(Request $request, string $module_id, string $video_id): JsonResponse
    {
        $this->restrictFree();

        $video = Video::findOrFail($video_id);
        $user_id = Auth::user()->id;

        if ($is_book_marked = $video->bookmarks()->where('user_id', $user_id)->first()) {
            $is_book_marked->delete();
            $status = 'unsaved';
        } else {
            $bookmark = new BookMark(['module_id' => $module_id, 'user_id' => $user_id]);
            $video->bookmarks()->save($bookmark);
            $status = 'saved';
        }

        return response()->json(
            [
                'success' => true,
                'message' => "Video $status successful",
                'status' => $status,
                'video' => $this->videoService->getDetail($video_id, $module_id),
            ]);
    }
}
