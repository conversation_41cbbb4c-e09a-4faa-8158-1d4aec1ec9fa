<?php

namespace App\Http\Controllers;

use App\Models\TempAuthKey;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class TempAuthKeysController extends Controller
{
    public function generateKey(Request $request)
    {

        $userId = auth()->user()->id;
        TempAuthKey::where('user_id', $userId)->delete();

        $rawKey = Str::random(40);

        TempAuthKey::create([
            'user_id' => $userId,
            'key' => hash('sha256', $rawKey),
            'expires_at' => now()->addMinutes(5),
        ]);

        if (config('app.server_env') == 'production') {
            $web_url = 'https://akinaone.com/temp-login';
        } else if (config('app.server_env') == 'staging') {
            $web_url = 'https://web-staging.akinaone.com/temp-login';
        } else {
            $web_url = 'https://web-dev.akinaone.com/temp-login';
        }

        return response()->json([
            'success' => true,
            'message' => 'User verified successfully.',
            'key' => $rawKey,
            'web_url' => $web_url
        ]);
    }

    public function verifyKey(Request $request)
    {
        $request->validate([
            'key' => 'required|string'
        ]);

        $rawKey = $request->key;

        $hashedKey = hash('sha256', $rawKey);

        $tempKey = TempAuthKey::where('key', $hashedKey)
            ->where('expires_at', '>', now())
            ->first();

        if (!$tempKey) {
            return response()->json(['error' => 'Expired or invalid key.'], 401);
        }

        $user = User::find($tempKey->user_id);
        $tempKey->verified_at = Carbon::now();
        $tempKey->save();
        $tempKey->delete();

        $token = $user->createToken($user->first_name, ['role:user']);
        return response()->json([
            'success' => true,
            'message' => 'User verified successfully.',
            'token' => $token->plainTextToken,
            'user' => $user
        ]);
    }
}
