<?php

namespace App\Http\Controllers;

use App\Http\Requests\EmpowerHerTypePostRequest;
use App\Models\EmpowerHerPost;
use App\Models\EmpowerHerType;
use App\Models\Module;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class EmpowerHerTypesController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(string $module_id, Request $request): \Illuminate\Http\JsonResponse
    {
        $module = Module::where('type', 'event')->where('id', $module_id)->firstOrFail();

        $types = EmpowerHerType::where('module_id', $module_id)->get();

        return response()->json(
            [
                'success' => true,
                'types' => $types,
                'module' => $module,
            ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(EmpowerHerTypePostRequest $request): \Illuminate\Http\JsonResponse
    {
        $data = $request->only([
            'title',
            'description',
            'module_id',
        ]);

        $thumbnail_extension = request()->file('thumbnail')->getClientOriginalExtension();
        $image_name = time() . '_' . Str::slug($request->title) . '-thumbnail.' . $thumbnail_extension;

        $thumbnail_path = $request->file('thumbnail')->storeAs(
            'empower_her_photos',
            $image_name,
            's3'
        );

        $data['thumbnail'] = \config('filesystems.disks.s3.url') . $thumbnail_path;

        $types_data = EmpowerHerType::create(
            $data
        )->toArray();

        return response()->json(
            [
                'success' => true,
                'message' => 'Empower her type added successfully',
                'music_data' => $types_data,
            ]);
    }
}
