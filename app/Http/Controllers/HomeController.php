<?php

namespace App\Http\Controllers;

use App\Models\Module;
use Illuminate\Http\JsonResponse;

class HomeController extends Controller
{
    public function index(): JsonResponse
    {
        $modules = Module::whereNotIn('type', ['ask_akina', 'event', 'event_new', 'coming_soon', 'social-connect'])->get();

        return response()->json(
            [
                'success' => true,
                'modules' => $modules,
            ]);
    }
}
