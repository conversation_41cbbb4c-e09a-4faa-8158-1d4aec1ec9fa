<?php

namespace App\Http\Controllers;

use App\Http\Requests\PageSectionStoreRequest;
use App\Models\Page;
use App\Models\PageSection;
use App\Models\PageSectionDetail;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class PageSectionDetailsController extends Controller
{
    public function store(string $page_id, string $page_section_id, PageSectionStoreRequest $request)
    {
        $page = Page::findOrFail($page_id);
        $pageSection = PageSection::findOrFail($page_section_id);

        $data = $request->only([
            'title',
            'sub_title',
            'description',
            'anchor_text',
            'asset_type',
            'anchor_link',
        ]);

        if ($request->hasFile('asset_file')) {

            $extension = request()->file('asset_file')->getClientOriginalExtension();
            $image_name = time() . '_' . Str::slug($request->title) . '-asset.' . $extension;

            $path = $request->file('asset_file')->storeAs(
                'pages/assets',
                $image_name,
                's3'
            );

            $data['asset_link'] = \config('filesystems.disks.s3.url') . $path;
        }
        $data['page_section_id'] = $pageSection->id;

        $page_data = PageSectionDetail::create(
            $data
        );

        return response()->json(
            [
                'success' => true,
                'message' => 'Page added successfully',
                'page_data' => $page_data,
            ]);
    }
}
