<?php

namespace App\Http\Controllers;

use App\Http\Requests\EventAddRequest;
use App\Models\Event;
use App\Models\Location;
use App\Models\Module;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class EventsController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(string $module_id, Request $request): \Illuminate\Http\JsonResponse
    {
        $module = Module::where('type', 'event_new')->where('id', $module_id)->firstOrFail();

        $locations = Location::where('module_id', $module_id)->whereHas('events')->get();

        return response()->json(
            [
                'success' => true,
                'module' => $module,
                'locations' => $locations,
            ]);
    }

    /**
     * Display a listing of the resource.
     */
    public function showLocation(string $module_id, string $location_id, Request $request): \Illuminate\Http\JsonResponse
    {
        $location = Location::findOrFail($location_id);

        $location_events = Event::where('location_id', $location_id)->where('is_akina_event', false)->get();
        $akina_events = Event::where('location_id', $location_id)->where('is_akina_event', true)->get();

        return response()->json(
            [
                'success' => true,
                'location' => $location,
                'location_events' => $location_events,
                'akina_events' => $akina_events,
            ]);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $module_id, string $event_id)
    {
        $event = Event::findOrFail($event_id);

        return response()->json(
            [
                'success' => true,
                'event' => $event
            ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(EventAddRequest $request): \Illuminate\Http\JsonResponse
    {
        $data = $request->only([
            'title',
            'content',
            'sub_title',
            'module_id',
            'button_text',
            'button_link',
            'location_id',
            'is_akina_event',
            'event_date',
        ]);

        if ($request->hasFile('thumbnail')) {

            $extension = request()->file('thumbnail')->getClientOriginalExtension();
            $image_name = time() . '_' . Str::slug($request->title) . '-thumbnail.' . $extension;

            $path = $request->file('thumbnail')->storeAs(
                'event_photos',
                $image_name,
                's3'
            );

            $data['thumbnail'] = \config('filesystems.disks.s3.url') . $path;
        }

        $post_data = Event::create(
            $data
        )->toArray();

        return response()->json(
            [
                'success' => true,
                'message' => 'Event added successfully',
                'event_data' => $post_data,
            ]);
    }
}
