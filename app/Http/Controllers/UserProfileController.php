<?php

namespace App\Http\Controllers;

use App\Http\Requests\UserProfileUpdateRequest;
use App\Models\User;
use App\Models\UserProfile;
use App\Services\PushNotificationService;
use App\Services\ReferralCodeService;
use App\Services\SocialConnectPostService;
use App\Traits\WeeklyRequestLimiter;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;

class UserProfileController extends Controller
{
    use WeeklyRequestLimiter;

    protected PushNotificationService $pushNotificationService;
    protected SocialConnectPostService $connectPostService;
    protected ReferralCodeService $referralCodeService;

    public function __construct(PushNotificationService $pushNotificationService, SocialConnectPostService $connectPostService, ReferralCodeService $referralCodeService)
    {
        $this->pushNotificationService = $pushNotificationService;
        $this->connectPostService = $connectPostService;
        $this->referralCodeService = $referralCodeService;
    }

    public function show(): JsonResponse
    {
        $user = Auth::user();

        $followers_count = $this->connectPostService->getFollowersCount($user);
        $posts_count = $this->connectPostService->getUserPostsCount($user);
        $notifications_count = $this->pushNotificationService->unreadNotificationsCount($user->id);
        $share_able_referral_code = $this->referralCodeService->getUserShareAbleReferralCode($user);
        $subscription = $user->subscriptions->first();

        return response()->json(
            [
                'success' => true,
                'user' => $user,
                'followers_count' => $followers_count,
                'posts_count' => $posts_count,
                'notifications_count' => $notifications_count,
                'share_able_referral_code' => $share_able_referral_code,
                'subscription' => $subscription,
            ]);
    }

    /**
     * @OA\Get(
     *      path="/get_profile",
     *      operationId="getProfile",
     *      tags={"User Profile"},
     *      summary="Get Profile",
     *      description="Get user profile selected options",
     *      @OA\Response(
     *      response=200,
     *      description="Example of Success response",
     *      @OA\JsonContent(
     *
     *     @OA\Property(property="success", type="booleon", example="success"),
     *     @OA\Property(property="user_profile1", type="object", example={{
     *                    "first_name": "Ansar",
     *                    "last_name": "Khalil",
     *                    "email": "<EMAIL>",
     *                    "profile_photo": false
     *                  }},
     *              ),
     *     @OA\Property(
     *                 property="education_level",
     *                 type="array",
     *                 example={{
     *                   "name": "high_school_diploma",
     *                   "value": "High School Diploma",
     *                   "selected": false
     *                 }, {
     *                    "name": "associate_degree",
     *                    "value": "Associate’s Degree",
     *                    "selected": true
     *                  },{
     *                      "name": "some_college",
     *                      "value": "Some College",
     *                      "selected": false
     *                   }},
     *                 @OA\Items(
     *
     *                 ),
     *              ),
     *
     *     @OA\Property(
     *                   property="income_level",
     *                   type="array",
     *                   example={{
     *                     "name": "under_25000",
     *                     "value": "Under $25,000",
     *                     "selected": false
     *                   }, {
     *                      "name": "25000_49999",
     *                      "value": "$25,000 - $49,999",
     *                      "selected": true
     *                    },{
     *                        "name": "75000_99999",
     *                        "value": "$75,000 - $99,999",
     *                        "selected": false
     *                     }},
     *                   @OA\Items(
     *
     *                   ),
     *                ),
     *
     *        ),
     *
     *
     *         ),
     *
     *        @OA\Response(
     *      response=401,
     *      description="Example of Bad Request",
     *      @OA\JsonContent(
     *      @OA\Property(property="message", type="string", example="Unauthenticated")
     *        )
     *     )
     * )
     *
     */
    public function showV2(Request $request): JsonResponse
    {
        $user = Auth::user();
        $ai_request_limit = $this->checkRequestLimit('akina_ai');
        $followers_count = $this->connectPostService->getFollowersCount($user);
        $posts_count = $this->connectPostService->getUserPostsCount($user);
        $user_profile = $user?->profile;
        $all_options = config('profileoptions');
        $response = $user_profile ? $user_profile->only('bio', 'date_of_birth', 'address', 'phone_number', 'nick_name', 'is_terms_accepted', 'is_completed') : [];
        $response['first_name'] = $user->first_name;
        $response['last_name'] = $user->last_name;
        $response['profile_photo'] = $user->profile_photo;
        $response['cover_photo'] = $user->cover_photo;
        $response['user_name'] = $user->user_name;
        $response['user_type'] = $user->user_type;
        $response['user_membership_type'] = $user->user_membership_type;
        $response['email'] = $user->email;
        $response['id'] = $user->id;
        $response['fcm_tokens'] = $user->fcmTokens;
        $response['ai_request_limit'] = $ai_request_limit;
        $response['followers_count'] = $followers_count;
        $response['sc_posts_count'] = $posts_count;
        $response['notifications_count'] = $this->pushNotificationService->unreadNotificationsCount($user->id);
        $response['share_able_referral_code'] = $this->referralCodeService->getUserShareAbleReferralCode($user);
        $response['subscription'] = $user->subscriptions->first();

        foreach ($all_options as $key => $value) {
            $response[$key] = Arr::map($value, function ($value) use ($user_profile, $key) {
                $value['selected'] = false;
                if ($user_profile) {
                    if ($value['name'] === $user_profile[$key]) {
                        $value['selected'] = true;
                    }
                }
                return $value;
            });
        }

        return response()->json(
            [
                'success' => true,
                'user_profile' => $response,
            ]);
    }

    public function showWpUsers(Request $request): JsonResponse
    {
        $user = Auth::user();

        $users = User::where('type', 'wordpress')->get()->makeVisible(['email_verified_at']);

        return response()->json(
            [
                'success' => true,
                'users' => $users,
            ]);
    }

    public function options(UserProfile $userProfile): JsonResponse
    {
        return response()->json(
            [
                'success' => true,
                'options' => config('profileoptions'),
            ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UserProfileUpdateRequest $request)
    {
        $user = Auth::user();
        $user_id = $user?->id;

        $data = $request->only([
            'education_level',
            'income_level',
            'marital_status',
            'children_option',
            'interests_option',
            'joining_motivation_options',
            'date_of_birth',
            'address',
            'nick_name',
            'phone_number',
            'bio',
            'is_terms_accepted',
        ]);

        if (!empty($data)) {
            $data['is_completed'] = true;
        }

        $data['user_id'] = $user_id;

        $userData = $request->only([
            'first_name',
            'last_name',
        ]);

        if ($request->has('user_name')) {
            $userData['user_name'] = $request->user_name;
        }

        if ($request->boolean('remove_profile_photo')) {
            $userData['profile_photo'] = null;
        }

        if ($request->boolean('remove_cover_photo')) {
            $userData['cover_photo'] = null;
        }

        if ($request->hasFile('profile_photo')) {

            $extension = request()->file('profile_photo')->getClientOriginalExtension();
            $image_name = time() . '_' . $user->first_name . '-photo.' . $extension;
            $image_path = 'profile_photos/' . $image_name;
            $path = $request->file('profile_photo')->storeAs(
                'profile_photos',
                $image_name,
                's3'
            );

            $userData['profile_photo'] = \config('filesystems.disks.s3.url') . $path;
        }

        if ($request->hasFile('cover_photo')) {

            $extension = request()->file('cover_photo')->getClientOriginalExtension();
            $image_name = time() . '_' . $user->first_name . '-cover-photo.' . $extension;
            $image_path = 'cover_photos/' . $image_name;
            $path = $request->file('cover_photo')->storeAs(
                'cover_photos',
                $image_name,
                's3'
            );

            $userData['cover_photo'] = \config('filesystems.disks.s3.url') . $path;
        }

        $user_data = User::find($user_id)
            ->update($userData);

        if (!empty($userData)) {
            $data['is_completed'] = true;
        }

        $profile_data = UserProfile::updateOrCreate(
            ['user_id' => $user_id],
            $data
        )->toArray();

        $user = User::find($user_id);

        $response = array_merge($user->toArray(), $profile_data);

        return response()->json(
            [
                'success' => true,
                'message' => 'updated user profile successfully',
                'profile_data' => $response,
            ]);
    }
}
