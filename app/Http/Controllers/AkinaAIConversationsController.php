<?php

namespace App\Http\Controllers;

use App\Http\Requests\AkinaAISocketRequest;
use App\Http\Requests\LikeAIMessageRequest;
use App\Jobs\AkinaAICallJob;
use App\Models\AkinaAIConversation;
use App\Models\AkinaAIMessage;
use App\Services\AkinaAIService;
use App\Traits\WeeklyRequestLimiter;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use OpenAI;

class AkinaAIConversationsController extends Controller
{
    use WeeklyRequestLimiter;

    protected AkinaAIService $akinaAIService;

    public function __construct(AkinaAIService $akinaAIService)
    {
        $this->akinaAIService = $akinaAIService;
    }

    /*
     * List users conversations
     */
    public function index(Request $request): JsonResponse
    {
        $request_limit = $this->checkRequestLimit();
        $user = auth()->user();

        $conversations = AkinaAIConversation::where("user_id", $user->id)->orderByDesc('id')->get();

        return response()->json(['status' => true, 'request_limit' => $request_limit, 'conversations' => $conversations]);
    }

    /*
     * Start and store conversation
     */
    public function store(AkinaAISocketRequest $request)
    {
        $request_limit = $this->checkAndIncrementRequestLimit();

        $user = auth()->user();

        if ($request->conversation_id) {
            $conversation = AkinaAIConversation::with('messages')->select('id')->find($request->conversation_id);
            $conversation_id = $conversation->id;

            $messages = $conversation->messages->map(function ($chat) {
                return [
                    'role' => $chat->sender_type === 'ai' ? 'assistant' : 'user', // 'user' or 'assistant'
                    'content' => $chat->content,
                ];
            })->toArray();

        } else {
            $conversation = AkinaAIConversation::create(['user_id' => $user->id, 'title' => $request->message]);
            $conversation_id = $conversation['id'];
        }

        // store user message
        $user_message = AkinaAIMessage::create([
            'conversation_id' => $conversation_id,
            'sender_type' => 'user',
            'user_id' => $user->id,
            'content' => $request->message,
        ]);

        $messages[] = [
            'role' => 'user',
            'content' => $request->message,
        ];

        AkinaAICallJob::dispatch($user, $conversation_id, $request->message, $messages);

        return response()->json(['status' => true, 'request_limit' => $request_limit, 'message' => $user_message, 'conversation_id' => $conversation_id]);
    }

    /*
     * Show conversation detail
     */
    public function show($module_id, $conversation_id): JsonResponse
    {
        $conversation = AkinaAIConversation::with('messages')
            ->where('external_conversation_id', $conversation_id)
            ->first();

        if (!$conversation) $conversation = AkinaAIConversation::with('messages')->findOrFail($conversation_id);

        if ($conversation->user_id !== auth()->id()) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        return response()->json($conversation);
    }

    /*
     * Conversation with stream option
     */
    public function conversationStream(Request $request): \Symfony\Component\HttpFoundation\StreamedResponse
    {
        $validated = $request->validate([
            'conversation_id' => 'nullable|exists:akina_ai_conversations,id',
            'message' => 'required|string',
        ]);

        $user = auth()->user();

        // Create a new conversation if not provided
        $conversation = $request->conversation_id
            ? AkinaAIConversation::find($request->conversation_id)
            : AkinaAIConversation::create(['user_id' => $user->id]);

        // Store user's message
        AkinaAIMessage::create([
            'conversation_id' => $conversation->id,
            'user_id' => $user->id,
            'sender_type' => 'user',
            'content' => $validated['message'],
        ]);

        $client = OpenAI::client(config('openai.api_key'));

        $stream = $client->chat()->createStreamed([
            'model' => 'gpt-3.5-turbo', // or gpt-3.5-turbo
            'messages' => [
                ['role' => 'system', 'content' => 'You are a helpful assistant.'],
                ['role' => 'user', 'content' => $validated['message']],
            ],
            'stream' => true,
        ]);


        return response()->stream(function () use ($stream, $conversation) {
            //ob_start();
            if (ob_get_level() === 0) {
                ob_start(); // Start buffering if it's not active
            }
            $fullResponse = ''; // Accumulate the full AI response here

            foreach ($stream as $response) {
                $content = $response->choices[0]->delta->content ?? '';

                //logger()->info('Stream content:', [$content]);
                $fullResponse .= $content;

                echo $content;
                //ob_flush();
                flush();
            }
            // Store the full response in the database after the stream completes
            if (!empty($fullResponse)) {
                AkinaAIMessage::create([
                    'conversation_id' => $conversation->id,
                    'sender_type' => 'ai',
                    'content' => $fullResponse,
                ]);
            }
            //ob_end_flush(); // End output buffering
        }, 200, [
            'Content-Type' => 'text/event-stream',
            'Cache-Control' => 'no-cache',
            'Connection' => 'keep-alive',
        ]);
    }

    public function likeMessage(LikeAIMessageRequest $request, string $module_id, string $message_id): JsonResponse
    {
        $message = AkinaAIMessage::where('external_message_id', $message_id)->firstOrFail();
        $message->like_status = $request->like_status;
        $message->save();

        return response()->json([
            'success' => true,
            'message' => 'Message like status updated successfully',
            'data' => $message
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $module_id, string $conversation_id): JsonResponse
    {

        $conversation = AkinaAIConversation::findOrFail($conversation_id);
        $user_id = Auth::user()->id;

        if ($conversation->user_id !== $user_id) {
            return response()->json(
                [
                    'success' => false,
                    'message' => "You are not allowed to delete this conversation",
                ], 403);
        }
        $conversation->delete();

        return response()->json(
            [
                'success' => true,
                'message' => "Conversation deleted successful"
            ]);
    }
}
