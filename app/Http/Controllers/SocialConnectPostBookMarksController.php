<?php

namespace App\Http\Controllers;

use App\Models\BookMark;
use App\Models\SocialConnectPost;
use App\Services\SocialConnectPostService;
use App\Traits\RestrictFreeUser;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class SocialConnectPostBookMarksController extends Controller
{
    use RestrictFreeUser;

    private SocialConnectPostService $socialConnectPostService;

    public function __construct(SocialConnectPostService $socialConnectPostService)
    {
        $this->socialConnectPostService = $socialConnectPostService;
    }

    /**
     * Store a newly created resource in storage.
     */
    public function bookMark(Request $request, string $post_id): JsonResponse
    {
        $this->restrictFree();

        $post = SocialConnectPost::findOrFail($post_id);
        $user_id = Auth::user()->id;

        if ($is_book_marked = $post->bookmarks()->where('user_id', $user_id)->first()) {
            $is_book_marked->delete();
            $status = 'unsaved';
        } else {
            $like = new BookMark(['module_id' => $post->module_id, 'user_id' => $user_id]);
            $post->bookmarks()->save($like);
            $status = 'saved';
        }

        return response()->json(
            [
                'success' => true,
                'message' => "SC post $status successful",
                'status' => $status,
                'post' => $this->socialConnectPostService->getDetail($post_id),
            ]);
    }
}
