<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;

class ResetPasswordController extends Controller
{
    public function resetPassword(Request $request): \Illuminate\Http\JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'current_password' => 'required',
            'password' => 'required|confirmed',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $user = Auth::user();

        if ($user) {
            if (Hash::check($request->password, $user->password)) {
                return response()->json(['success' => false, 'message' => 'New password cannot be the same as your old password.']);
            }
            if (Hash::check($request->current_password, $user->password)) {
                $user->password = bcrypt($request->password);
                $user->save();

                return response()->json(
                    [
                        'success' => true,
                        'message' => 'User password reset is successful.',
                    ]
                );
            } else {
                return response()->json(['success' => false, 'message' => 'Invalid current password.']);
            }
        }

        return response()->json(['success' => false, 'message' => 'User is unauthenticated.']);
    }
}
