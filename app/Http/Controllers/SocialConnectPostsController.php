<?php

namespace App\Http\Controllers;

use App\Http\Requests\SocialConnectPostStoreRequest;
use App\Http\Requests\SocialConnectPostUpdateRequest;
use App\Http\Resources\SocialConnectPostResourceCollection;
use App\Models\BlockedUser;
use App\Models\Module;
use App\Models\SocialConnectPost;
use App\Models\UserFollower;
use App\Services\SocialConnectPostService;
use App\Traits\RestrictFreeUser;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class SocialConnectPostsController extends Controller
{
    use RestrictFreeUser;

    private SocialConnectPostService $socialConnectPostService;

    public function __construct(SocialConnectPostService $socialConnectPostService)
    {
        $this->socialConnectPostService = $socialConnectPostService;
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $limit = $request->limit ?? 5;
        $user = Auth::user();
        $blockedUserIds = BlockedUser::where('blocked_by', $user->id)
            ->pluck('user_id');

        $posts = SocialConnectPost::whereNotIn('user_id', $blockedUserIds)
            ->with(['likes' => function ($q) use ($user) {
                $q->where('user_id', $user->id);
            }])
            ->with(['bookmarks' => function ($q) use ($user) {
                $q->where('user_id', $user->id);
            }])
            ->with('user')
            ->whereDoesntHave('reported', function ($q) use ($user) {
                $q->where('reported_by', $user->id);
            })
            ->withCount('bookmarks')
            ->withCount('comments')
            ->withCount('likes')
            ->orderBy('id', 'desc')
            ->paginate($limit);

        return new SocialConnectPostResourceCollection($posts);
    }

    /**
     * Display a listing of the resource.
     */
    public function indexFromFollowersOnly(): SocialConnectPostResourceCollection
    {
        $limit = $request->limit ?? 5;
        $user = Auth::user();
        $followerIds = UserFollower::where('follower_id', $user->id)
            ->pluck('user_id');

        $followerIds->add($user->id);

        $blockedUserIds = BlockedUser::where('blocked_by', $user->id)
            ->pluck('user_id');

        $allowedUserIds = $followerIds->diff($blockedUserIds);

        $posts = SocialConnectPost::whereIn('user_id', $allowedUserIds)
            ->with(['likes' => function ($q) use ($user) {
                $q->where('user_id', $user->id);
            }])
            ->with(['bookmarks' => function ($q) use ($user) {
                $q->where('user_id', $user->id);
            }])
            ->with('user')
            ->whereDoesntHave('reported', function ($q) use ($user) {
                $q->where('reported_by', $user->id);
            })
            ->withCount('bookmarks')
            ->withCount('comments')
            ->withCount('likes')
            ->orderBy('id', 'desc')
            ->paginate($limit);

        return new SocialConnectPostResourceCollection($posts);
    }

    public function getPostsByUser(string $user_id): SocialConnectPostResourceCollection
    {
        $limit = $request->limit ?? 5;
        $user = Auth::user();

        $posts = SocialConnectPost::with(['likes' => function ($q) use ($user) {
            $q->where('user_id', $user->id);
        }])
            ->with(['bookmarks' => function ($q) use ($user) {
                $q->where('user_id', $user->id);
            }])
            ->with('user')
            ->whereDoesntHave('reported', function ($q) use ($user) {
                $q->where('reported_by', $user->id);
            })
            ->withCount('bookmarks')
            ->withCount('comments')
            ->withCount('likes')
            ->orderBy('id', 'desc')
            ->where('user_id', $user_id)
            ->paginate($limit);

        return new SocialConnectPostResourceCollection($posts);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(SocialConnectPostStoreRequest $request): \Illuminate\Http\JsonResponse
    {
        $this->restrictFree();

        $user = Auth::user();
        $module = Module::where('slug', 'social-connect')->firstOrFail();

        $socialConnectPost = new SocialConnectPost();
        $socialConnectPost->user_id = $user->id;
        $socialConnectPost->module_id = $module->id;
        $socialConnectPost->type = $request->type;
        $socialConnectPost->text = $request->text;

        if ($socialConnectPost->type === 'post' && $request->hasFile('image_file')) {
            $imageTitle = $request->text ? substr($request->text, 0, 10) : 'n-text';
            $extension = request()->file('image_file')->getClientOriginalExtension();
            $image_name = time() . '_' . Str::slug($imageTitle) . '-sc-photo.' . $extension;

            $path = $request->file('image_file')->storeAs(
                'sc_photos',
                $image_name,
                's3'
            );

            $socialConnectPost->media_link = \config('filesystems.disks.s3.url') . $path;
        } else if ($socialConnectPost->type === 'story' && $request->hasFile('video_file')) {
            $videoTitle = $request->text ? substr($request->text, 0, 10) : 'n-text';
            $extension = request()->file('video_file')->getClientOriginalExtension();
            $image_name = time() . '_' . Str::slug($videoTitle) . '-sc-video.' . $extension;

            $path = $request->file('video_file')->storeAs(
                'sc_videos',
                $image_name,
                's3'
            );

            $socialConnectPost->media_link = \config('filesystems.disks.s3.url') . $path;

            if ($request->hasFile('video_thumbnail')) {
                $videoThumbnailTitle = $request->text ? substr($request->text, 0, 10) : 'n-text';
                $extension = request()->file('video_thumbnail')->getClientOriginalExtension();
                $image_name = time() . '_' . Str::slug($videoThumbnailTitle) . '-sc-video-thumbnail.' . $extension;

                $path = $request->file('video_thumbnail')->storeAs(
                    'sc_videos_thumbnail',
                    $image_name,
                    's3'
                );

                $socialConnectPost->video_thumbnail = \config('filesystems.disks.s3.url') . $path;
            }
        }

        $socialConnectPost->save();

        return response()->json(
            [
                'success' => true,
                'message' => 'Social connect post added successfully',
                'post_data' => $socialConnectPost,
            ]);
    }


    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        return $this->socialConnectPostService->getDetail($id);
    }

    public function update(SocialConnectPostUpdateRequest $request, string $post_id): \Illuminate\Http\JsonResponse
    {
        $this->restrictFree();

        $user = Auth::user();
        $socialConnectPost = SocialConnectPost::findOrFail($post_id);

        if ($socialConnectPost->user_id !== $user->id) {
            return response()->json(
                [
                    'success' => false,
                    'message' => "You are not allowed to edit this post",
                ], 403);
        }

        $socialConnectPost->type = $request->type;
        $socialConnectPost->text = $request->has('text') ? $request->text : $socialConnectPost->text;
        $socialConnectPost->user_id = $user->id;

        if ($socialConnectPost->type === 'post' && $request->hasFile('image_file')) {
            $imageTitle = $request->text ? substr($request->text, 0, 10) : 'n-text';
            $extension = request()->file('image_file')->getClientOriginalExtension();
            $image_name = time() . '_' . Str::slug($imageTitle) . '-sc-photo.' . $extension;

            $path = $request->file('image_file')->storeAs(
                'sc_photos',
                $image_name,
                's3'
            );

            $socialConnectPost->media_link = \config('filesystems.disks.s3.url') . $path;
        } else if ($socialConnectPost->type === 'story' && $request->hasFile('video_file')) {
            $videoTitle = $request->text ? substr($request->text, 0, 10) : 'n-text';
            $extension = request()->file('video_file')->getClientOriginalExtension();
            $image_name = time() . '_' . Str::slug($videoTitle) . '-sc-video.' . $extension;

            $path = $request->file('video_file')->storeAs(
                'sc_videos',
                $image_name,
                's3'
            );

            $socialConnectPost->media_link = \config('filesystems.disks.s3.url') . $path;
        }

        $socialConnectPost->save();

        return response()->json(
            [
                'success' => true,
                'message' => 'Social connect post updated successfully',
                'post_data' => $socialConnectPost,
            ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $post_id)
    {
        $user = Auth::user();
        $socialConnectPost = SocialConnectPost::findOrFail($post_id);

        if ($socialConnectPost->user_id !== $user->id) {
            return response()->json(
                [
                    'success' => false,
                    'message' => "You are not allowed to delete this post",
                ], 403);
        }

        $socialConnectPost->delete();

        return response()->json(
            [
                'success' => true,
                'message' => 'Social connect post deleted successfully',
            ]);
    }
}
