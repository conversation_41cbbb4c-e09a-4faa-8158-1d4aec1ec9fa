<?php

namespace App\Http\Controllers;

use App\Http\Requests\CommentReplyPatchRequest;
use App\Http\Requests\CommentReplyPostRequest;
use App\Jobs\SendSingleUserNotificationJob;
use App\Models\Comment;
use App\Models\CommentReply;
use App\Models\Module;
use App\Services\PostService;
use App\Traits\RestrictFreeUser;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class CommentRepliesController extends Controller
{
    use RestrictFreeUser;
    private PostService $postService;

    public function __construct(PostService $postService)
    {
        $this->postService = $postService;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(CommentReplyPostRequest $request, string $module_id, string $post_id, string $comment_id): JsonResponse
    {
        $this->restrictFree();

        $comment = Comment::findOrFail($comment_id);
        $user_id = Auth::user()->id;

        $commentReply = new CommentReply(['reply' => $request->reply, 'comment_id' => $comment_id, 'user_id' => $user_id]);
        $comment->replies()->save($commentReply);

        $this->notifyUserAboutReply($comment, $commentReply, $request->reply);

        return response()->json(
            [
                'success' => true,
                'message' => "Comment reply added successful",
                'post' => $this->postService->getDetail($post_id)
            ]);
    }

    private function notifyUserAboutReply(Comment $comment, CommentReply $reply, string $replyText)
    {
        $post = $comment->commentable;
        $user_id = Auth::user()->id;
        $repliedBy = Auth::user();
        $replierFullName = "{$repliedBy->first_name} {$repliedBy->last_name}";
        $module = Module::where('id', $post->module_id)->first();

        // Notify the comment owner
        if ($comment->user_id !== $user_id) {
            $commentOwner = $comment->user;

            SendSingleUserNotificationJob::dispatch(
                $commentOwner->id,
                "{$replierFullName} replied to your comment",
                "{$replierFullName} replied: \"{$replyText}\"",
                [
                    'post_id' => (string)$post->id,
                    'comment_id' => (string)$comment->id,
                    'reply_id' => (string)$reply->id,
                    'url' => "akinaconnect://blog-detail//{$post->id}/{$post->module_id}/{$module->name}",
                    'web_url' => "/{$module->slug}/post/{$post->id}",
                ]
            );
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(CommentReplyPatchRequest $request, string $module_id, string $post_id, string $comment_id, string $reply_id)
    {
        $this->restrictFree();

        $user_id = Auth::user()->id;
        $commentReply = CommentReply::findOrFail($reply_id);

        if ($commentReply->user_id !== $user_id) {
            return response()->json(
                [
                    'success' => false,
                    'message' => "You are not allowed to update this comment reply",
                ], 403);
        }

        $commentReply->reply = $request->reply;
        $commentReply->save();

        return response()->json(
            [
                'success' => true,
                'message' => "Comment reply updated successful",
                'post' => $this->postService->getDetail($post_id)
            ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $module_id, string $post_id, string $comment_id, string $reply_id)
    {
        $user_id = Auth::user()->id;
        $commentReply = CommentReply::findOrFail($reply_id);

        if ($commentReply->user_id !== $user_id) {
            return response()->json(
                [
                    'success' => false,
                    'message' => "You are not allowed to delete this comment reply",
                ], 403);
        }

        $commentReply->delete();

        return response()->json(
            [
                'success' => true,
                'message' => "Comment reply deleted successful",
                'post' => $this->postService->getDetail($post_id)
            ]);
    }
}
