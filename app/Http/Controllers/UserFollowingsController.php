<?php

namespace App\Http\Controllers;

use App\Http\Resources\UserResourceCollection;
use App\Models\User;
use App\Models\UserFollower;
use Illuminate\Support\Facades\Auth;

class UserFollowingsController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(): UserResourceCollection
    {
        $limit = $request->limit ?? 10;
        $user = Auth::user();
        $followingIdsArray = $user ? $user->followings()->pluck('user_id'): collect();

        $followingIds = UserFollower::where('follower_id', $user->id)->pluck('user_id');
        $followings = User::whereIn('id', $followingIds)->paginate($limit);

        return new UserResourceCollection($followings, $followingIdsArray);
    }

    /**
     * Display a listing of the resource.
     */
    public function otherUsersFollowings(string $user_id): UserResourceCollection
    {
        $limit = $request->limit ?? 10;
        $user = User::findOrFail($user_id);
        $followingIdsArray = Auth::user() ? Auth::user()->followings()->pluck('user_id'): collect();


        $followingIds = UserFollower::where('follower_id', $user->id)->pluck('user_id');
        $followings = User::whereIn('id', $followingIds)->paginate($limit);

        return new UserResourceCollection($followings, $followingIdsArray);
    }
}
