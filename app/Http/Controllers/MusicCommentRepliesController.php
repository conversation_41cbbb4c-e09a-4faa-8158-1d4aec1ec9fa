<?php

namespace App\Http\Controllers;

use App\Http\Requests\CommentReplyPatchRequest;
use App\Http\Requests\CommentReplyPostRequest;
use App\Models\Comment;
use App\Models\CommentReply;
use App\Services\MusicService;
use App\Traits\RestrictFreeUser;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class MusicCommentRepliesController extends Controller
{
    use RestrictFreeUser;
    private MusicService $musicService;

    public function __construct(MusicService $musicService)
    {
        $this->musicService = $musicService;
    }
    /**
     * Store a newly created resource in storage.
     */
    public function store(CommentReplyPostRequest $request, string $module_id, string $music_id, string $comment_id): JsonResponse
    {
        $this->restrictFree();

        $comment = Comment::findOrFail($comment_id);
        $user_id = Auth::user()->id;

        $commentReply = new CommentReply(['reply' => $request->reply, 'comment_id' => $comment_id, 'user_id' => $user_id]);
        $comment->replies()->save($commentReply);

        return response()->json(
            [
                'success' => true,
                'message' => "Comment reply added successful",
                'music' => $this->musicService->getDetail($music_id, $module_id),
            ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(CommentReplyPatchRequest $request, string $module_id, string $music_id, string $comment_id, string $reply_id)
    {
        $this->restrictFree();

        $user_id = Auth::user()->id;
        $commentReply = CommentReply::findOrFail($reply_id);

        if ($commentReply->user_id !== $user_id) {
            return response()->json(
                [
                    'success' => false,
                    'message' => "You are not allowed to update this comment reply",
                ], 403);
        }

        $commentReply->reply = $request->reply;
        $commentReply->save();

        return response()->json(
            [
                'success' => true,
                'message' => "Comment reply updated successful",
                'music' => $this->musicService->getDetail($music_id, $module_id),
            ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $module_id, string $music_id, string $comment_id, string $reply_id)
    {
        $user_id = Auth::user()->id;
        $commentReply = CommentReply::findOrFail($reply_id);

        if ($commentReply->user_id !== $user_id) {
            return response()->json(
                [
                    'success' => false,
                    'message' => "You are not allowed to delete this comment reply",
                ], 403);
        }

        $commentReply->delete();

        return response()->json(
            [
                'success' => true,
                'message' => "Comment reply deleted successful",
                'music' => $this->musicService->getDetail($music_id, $module_id),
            ]);
    }
}
