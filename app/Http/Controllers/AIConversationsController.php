<?php

namespace App\Http\Controllers;

use App\Http\Requests\LikeAIMessageRequest;
use App\Models\AIConversation;
use App\Models\AIMessage;
use App\Traits\WeeklyRequestLimiter;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use OpenAI;

class AIConversationsController extends Controller
{
    use WeeklyRequestLimiter;


    /*
     * List users conversations
     */
    public function index(Request $request): JsonResponse
    {
        $request_limit = $this->checkRequestLimit();
        $user = auth()->user();

        $conversations = AIConversation::where("user_id", $user->id)->orderByDesc('id')->get();

        return response()->json(['status' => true, 'request_limit' => $request_limit, 'conversations' => $conversations]);
    }

    /*
     * Show conversation detail
     */
    public function show($module_id, $conversation_id): JsonResponse
    {
        $conversation = AIConversation::with('messages')
            ->where('external_conversation_id', $conversation_id)
            ->first();

        if (!$conversation) $conversation = AIConversation::with('messages')->findOrFail($conversation_id);

        if ($conversation->user_id !== auth()->id()) {
                 return response()->json(['error' => 'Unauthorized'], 403);
        }

        return response()->json($conversation);
    }

    public function likeMessage(LikeAIMessageRequest $request, string $module_id, string $message_id): JsonResponse
    {
        $message = AIMessage::where('external_message_id', $message_id)->firstOrFail();
        $message->like_status = $request->like_status;
        $message->save();

        return response()->json([
            'success' => true,
            'message' => 'Message like status updated successfully',
            'data' => $message
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $module_id, string $conversation_id): JsonResponse
    {

        $conversation = AIConversation::with('messages')
            ->where('external_conversation_id', $conversation_id)
            ->first();

        if (!$conversation) $conversation = AIConversation::with('messages')->findOrFail($conversation_id);
        $user_id = Auth::user()->id;

        if ($conversation->user_id !== $user_id) {
            return response()->json(
                [
                    'success' => false,
                    'message' => "You are not allowed to delete this conversation",
                ], 403);
        }
        $conversation->delete();

        return response()->json(
            [
                'success' => true,
                'message' => "Conversation deleted successful"
            ]);
    }
}
