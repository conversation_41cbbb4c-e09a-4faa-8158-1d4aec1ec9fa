<?php

namespace App\Http\Controllers;

use App\Models\Like;
use App\Models\Module;
use App\Models\Music;
use App\Models\News;
use App\Models\NewsSource;
use App\Models\PodCast;
use App\Models\Post;
use App\Models\Video;
use App\Services\PostService;
use App\Traits\RestrictFreeUser;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class LikesController extends Controller
{
    use RestrictFreeUser;
    private PostService $postService;

    public function __construct(PostService $postService)
    {
        $this->postService = $postService;
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $module_id = $request->module_id;
        $user = Auth::user();
        $user_id = $user?->id;

        $modules = Module::whereNot('type', 'coming_soon')->get();
        $newsSources = NewsSource::all();

        $postLikes = Post::whereHas('likes', function ($q) use ($user_id) {
            $q->where('user_id', $user_id);
        });
        $musicLikes = Music::whereHas('likes', function ($q) use ($user_id) {
            $q->where('user_id', $user_id);
        });
       /* $newsLikes = News::whereHas('likes', function ($q) use ($user_id) {
            $q->where('user_id', $user_id);
        });*/
        $videosLikes = Video::whereHas('likes', function ($q) use ($user_id) {
            $q->where('user_id', $user_id);
        });
        $podCastsLikes = PodCast::whereHas('likes', function ($q) use ($user_id) {
            $q->where('user_id', $user_id);
        });

        if ($module_id) {
            $postLikes = $postLikes->where('module_id', $module_id);;
            $musicLikes = $musicLikes->where('module_id', $module_id);
            //$newsLikes = $newsLikes->where('module_id', $module_id);
            $videosLikes = $videosLikes->where('module_id', $module_id);
            $podCastsLikes = $podCastsLikes->where('module_id', $module_id);
        }

        $postLikes = $postLikes->select('id', 'title', 'thumbnail', 'module_id')->get();
        $musicLikes = $musicLikes->select('id', 'title', 'thumbnail', 'module_id')->get();
        $videosLikes = $videosLikes->select('id', 'title', 'thumbnail', 'module_id')->get();
        $podCastsLikes = $podCastsLikes->select('id', 'title', 'thumbnail', 'module_id')->get();
        //$newsLikes = $newsLikes->select('id', 'news_source_id', 'title', 'thumbnail', 'module_id')->get();

        $allItems = $postLikes->mergeRecursive($musicLikes);
        //$allItems = $allItems->mergeRecursive($newsLikes);
        $allItems = $allItems->mergeRecursive($videosLikes);
        $allItems = $allItems->mergeRecursive($podCastsLikes);

        $allItems->map(function ($item) use ($modules, $newsSources) {
            $item['type'] = $modules->firstWhere('id', $item->module_id)->type;
            $item['module_title'] = $modules->firstWhere('id', $item->module_id)->title;
            if ($item['type'] == 'news') {
                $item['source_name'] = $newsSources->firstWhere('id', $item->news_source_id)->name;
            }
            return $item;
        });

        return response()->json(
            [
                'success' => true,
                'likes' => $allItems
            ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function like(Request $request, string $module_id, string $post_id): JsonResponse
    {
        $this->restrictFree();

        $post = Post::findOrFail($post_id);
        $user_id = Auth::user()->id;

        if ($is_liked = $post->likes()->where('user_id', $user_id)->first()) {
            $is_liked->delete();
            $status = 'unlike';
        } else {
            $like = new Like(['module_id' => $module_id, 'user_id' => $user_id]);
            $post->likes()->save($like);
            $status = 'like';
        }

        return response()->json(
            [
                'success' => true,
                'message' => "post $status successful",
                'status' => $status,
                'post' => $this->postService->getDetail($post_id)
            ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
