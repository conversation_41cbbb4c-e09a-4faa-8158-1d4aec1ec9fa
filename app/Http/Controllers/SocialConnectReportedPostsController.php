<?php

namespace App\Http\Controllers;

use App\Http\Requests\SocialConnectPostReportRequest;
use App\Models\SocialConnectReportedPost;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class SocialConnectReportedPostsController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(SocialConnectPostReportRequest $request)
    {
        $blockedPost = new SocialConnectReportedPost();
        $blockedPost->social_connect_post_id = $request->social_connect_post_id;
        $blockedPost->reported_by = Auth::user()->id;
        $blockedPost->report_reason_ids = $request->report_reason_ids;
        $blockedPost->save();

        return response()->json(
            [
                'success' => true,
                'message' => "Post reported successfully."
            ]);
    }

    /**
     * Display the specified resource.
     */
    public function show(SocialConnectReportedPost $socialConnectBlockedPost)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, SocialConnectReportedPost $socialConnectBlockedPost)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(SocialConnectReportedPost $socialConnectBlockedPost)
    {
        //
    }
}
