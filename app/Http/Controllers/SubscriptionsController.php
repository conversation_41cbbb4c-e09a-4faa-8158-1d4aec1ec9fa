<?php

namespace App\Http\Controllers;

use App\Enums\UserMembershipType;
use App\Http\Requests\CreateIntentRequest;
use App\Http\Requests\MakeMembershipRequest;
use App\Http\Requests\SubscriptionCancellationPostRequest;
use App\Services\MembershipService;
use Laravel\Cashier\Cashier;
use Stripe\Exception\ApiErrorException;
use Stripe\PaymentIntent;
use Stripe\Stripe;
use Stripe\StripeClient;

class SubscriptionsController extends Controller
{
    protected MembershipService $membershipService;

    public function __construct(MembershipService $membershipService)
    {
        $this->membershipService = $membershipService;
    }

    /**
     * @throws ApiErrorException
     */
    public function createSetupIntent(CreateIntentRequest $request)
    {
        $user = $request->user();
        $customer = $user->createOrGetStripeCustomer([
            'name' => $user->full_name,
            'email' => $user->email,
        ]);
        $stripeVersion = '2023-10-16';


        $stripe = new StripeClient(config('cashier.secret'));
        $ephemeralKey = $stripe->ephemeralKeys->create([
            'customer' => $customer->id,
        ], [
            'stripe_version' => $stripeVersion
        ]);
        $paymentIntent = null;


        Stripe::setApiKey(config('cashier.secret'));

        if ($this->membershipService->isPlanRecurring($request->price_id)) {
            // Create subscription with payment_behavior = incomplete
            $subscription = $stripe->subscriptions->create([
                'customer' => $user->stripe_id,
                'items' => [
                    ['price' => $request->price_id],
                ],
                'payment_behavior' => 'default_incomplete',
                'payment_settings' => ['save_default_payment_method' => 'on_subscription'],
                'expand' => ['latest_invoice.payment_intent'],
                'metadata' => [
                    'user_id' => $user->id,
                    'price_id' => $request->price_id,
                    'type' => UserMembershipType::STRIPE_SUBSCRIPTION->value,
                ],
            ]);

            $setupIntent = $subscription->latest_invoice->payment_intent;
        } else {
            $setupIntent = $stripe->setupIntents->create([
                'customer' => $user->stripe_id,
                'automatic_payment_methods' => ['enabled' => true],
            ]);

            $amountInCents = $this->membershipService->getAmountFromPrice($request->price_id);
            $paymentIntent = PaymentIntent::create([
                'amount' => $amountInCents, // Stripe uses cents
                'currency' => $request->currency,
                'customer' => $user->stripe_id,
                //'setup_future_usage' => 'off_session',
                'automatic_payment_methods' => [
                    'enabled' => true,
                ],
                'metadata' => [
                    'type' => $this->membershipService->isPlanRecurring($request->price_id)
                        ? UserMembershipType::STRIPE_SUBSCRIPTION->value
                        : UserMembershipType::STRIPE_ONE_TIME->value,
                    'price_id' => $request->price_id,
                    'user_id' => $user->id,
                ],
            ]);
        }

        $customer_session = $stripe->customerSessions->create([
            'customer' => $customer->id,
            'components' => [
                'payment_element' => [
                    'enabled' => true,
                    'features' => [
                        'payment_method_redisplay' => 'enabled',
                        'payment_method_save' => 'enabled',
                        'payment_method_save_usage' => 'on_session',
                        'payment_method_remove' => 'enabled',
                    ],
                ],
            ],
        ]);

        return response()->json([
            'success' => true,
            'payment_intent_client_secret' => $paymentIntent ? $paymentIntent->client_secret : '',
            'client_secret' => $setupIntent->client_secret,
            'customer_session_client_secret' => $customer_session->client_secret,
            'customer_id' => $customer->id,
            'ephemeral_key' => $ephemeralKey->secret,
            'publishable_key' => config('cashier.key')
        ]);
    }

    public function createSubscription(MakeMembershipRequest $request)
    {
        $user = $request->user();

        try {
            $isRecurring = $this->membershipService->isPlanRecurring($request->price_id);

            if ($isRecurring) {
                $subscription = $this->membershipService->createSubscription($user, $request->price_id, $request->payment_method);
            } else {
                $subscription = $this->membershipService->makeOneTimePayment($user, $request->price_id, $request->payment_method);
            }


            return response()->json([
                'success' => true,
                'subscription' => $subscription
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    public function plans()
    {
        $stripe = Cashier::stripe();
        $products = $stripe->products->all(['active' => true]);

        $plans = [];
        foreach ($products as $product) {
            $prices = $stripe->prices->all([
                'product' => $product->id,
                'active' => true
            ]);

            foreach ($prices as $price) {
                $plans[] = [
                    'product_id' => $product->id,
                    'price_id' => $price->id,
                    'product_name' => $product->name,
                    'price_amount' => $price->unit_amount / 100,
                    'price_currency' => $price->currency,
                    'interval' => $price->recurring->interval ?? 'one_time',
                ];
            }
        }

        return response()->json($plans);
    }

    public function getSubscriptionDetails()
    {
        $user = auth()->user();
        $subscription = $user->subscription('default');

        if ($subscription) {
            return response()->json([
                'success' => true,
                'subscription' => $subscription
            ]);
        } else {
            return response()->json([
                'success' => false,
                'message' => 'No active subscription found.'
            ], 404);
        }
    }

    public function cancelSubscription(SubscriptionCancellationPostRequest $request)
    {
        $user = $request->user();
        $activeSubscription = $user->subscriptions
            ->first(fn($subscription) => $subscription->active());

        if (!$activeSubscription) {
            return response()->json([
                'success' => false,
                'message' => 'No active subscription found.',
            ], 404);
        }

        if ($activeSubscription->onGracePeriod()) {
            return response()->json([
                'success' => false,
                'ends_at' => $activeSubscription->ends_at,
                'message' => 'Your subscription is already cancelled and will end on ' . $activeSubscription->ends_at->toDateString(),
            ]);
        }

        if ($activeSubscription->canceled()) {
            return response()->json([
                'success' => false,
                'message' => 'Your subscription has already been cancelled.',
            ]);
        }

        try {
            $this->membershipService->subscriptionCancellationRequest($activeSubscription, $request->reason);
            $activeSubscription->cancel();

            return response()->json([
                'success' => true,
                'ends_at' => $activeSubscription->ends_at,
                'message' => 'Subscription cancelled successfully. You will retain access until ' . $activeSubscription->ends_at->toDateString(),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }
}
