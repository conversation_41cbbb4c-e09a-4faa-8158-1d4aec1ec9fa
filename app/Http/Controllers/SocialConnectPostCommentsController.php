<?php

namespace App\Http\Controllers;

use App\Http\Requests\CommentPatchRequest;
use App\Http\Requests\CommentPostRequest;
use App\Models\BlockedUser;
use App\Models\Comment;
use App\Models\SocialConnectPost;
use App\Services\PushNotificationService;
use App\Services\SocialConnectPostService;
use App\Traits\RestrictFreeUser;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class SocialConnectPostCommentsController extends Controller
{
    use RestrictFreeUser;

    private SocialConnectPostService $socialConnectPostService;
    private PushNotificationService $pushNotificationService;

    public function __construct(SocialConnectPostService $socialConnectPostService, PushNotificationService $pushNotificationService)
    {
        $this->socialConnectPostService = $socialConnectPostService;
        $this->pushNotificationService = $pushNotificationService;
    }

    /**
     * Display a listing of the resource.
     */
    public function index(string $post_id)
    {
        $user_id = Auth::user()->id;
        $blockedUserIds = BlockedUser::where('blocked_by', $user_id)
            ->pluck('user_id');

        $comments = Comment::with('user')
            ->whereNotIn('user_id', $blockedUserIds)
            ->withExists(['reportedComment' => function ($q2) use ($user_id) {
                $q2->where('reported_by', $user_id);
            }])
            ->with([
                'replies' => function ($q) use ($blockedUserIds, $user_id) {
                    $q->whereNotIn('user_id', $blockedUserIds)
                        ->withExists(['reportedReply' => function ($q2) use ($user_id) {
                            $q2->where('reported_by', $user_id);
                        }])
                        ->with('user');
                }
            ])
            ->where('commentable_type', SocialConnectPost::class)
            ->where('commentable_id', $post_id)
            ->orderByDesc('created_at')
            ->get();

        return response()->json([
            'status' => true,
            'comments' => $comments
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(CommentPostRequest $request, string $post_id): JsonResponse
    {
        $this->restrictFree();

        $post = SocialConnectPost::findOrFail($post_id);
        $user = Auth::user();
        $user_id = $user->id;

        $like = new Comment(['comment' => $request->comment, 'module_id' => $post->module_id, 'user_id' => $user_id]);
        $comment = $post->comments()->save($like);

        $this->socialConnectPostService->sendNewCommentNotification($post, $comment, $user);

        return response()->json(
            [
                'success' => true,
                'message' => "Comment added successful",
                'comment' => $comment,
                'post' => $this->socialConnectPostService->getDetail($post_id)
            ]);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(CommentPatchRequest $request, string $post_id, string $comment_id)
    {
        $this->restrictFree();

        $user_id = Auth::user()->id;
        $comment = Comment::findOrFail($comment_id);

        if ($comment->user_id !== $user_id) {
            return response()->json(
                [
                    'success' => false,
                    'message' => "You are not allowed to update this comment",
                ], 403);
        }

        $comment->comment = $request->comment;
        $comment->save();

        return response()->json(
            [
                'success' => true,
                'message' => "Comment updated successful",
                'comment' => $comment,
                'post' => $this->socialConnectPostService->getDetail($post_id)
            ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $post_id, string $comment_id)
    {
        $user_id = Auth::user()->id;
        $comment = Comment::findOrFail($comment_id);

        if ($comment->user_id !== $user_id) {
            return response()->json(
                [
                    'success' => false,
                    'message' => "You are not allowed to delete this comment",
                ], 403);
        }
        $comment->delete();

        return response()->json(
            [
                'success' => true,
                'message' => "Comment deleted successful",
                'post' => $this->socialConnectPostService->getDetail($post_id)
            ]);
    }
}
