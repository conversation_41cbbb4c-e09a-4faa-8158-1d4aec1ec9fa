<?php

namespace App\Http\Controllers;

use App\Models\Plan;
use Auth;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class PlansController extends Controller
{
    public function index(Request $request): JsonResponse
    {
        $user = auth('sanctum')->user();

        if ($user) {
            $plans = Plan::where('is_student_plan', $user->is_student)
                ->orderBy('order', 'asc')
                ->get();
        } else {
            $plans = Plan::orderBy('order', 'asc')
                ->get();
        }
        return response()->json([
            'status' => 'success',
            'plans' => $plans,
        ]);
    }
}
