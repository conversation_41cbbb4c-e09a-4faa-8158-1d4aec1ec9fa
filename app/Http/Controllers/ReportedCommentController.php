<?php

namespace App\Http\Controllers;

use App\Http\Requests\ReportCommentRequest;
use App\Models\ReportedComment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ReportedCommentController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }


    /**
     * Store a newly created resource in storage.
     */
    public function store(ReportCommentRequest $request)
    {
        $reportedComment = new ReportedComment();
        $reportedComment->comment_id = $request->comment_id;
        $reportedComment->reported_by = Auth::user()->id;
        $reportedComment->report_reason_ids = $request->report_reason_ids;
        $reportedComment->save();

        return response()->json(
            [
                'success' => true,
                'message' => "Comment reported successfully."
            ]);
    }

    /**
     * Display the specified resource.
     */
    public function show(ReportedComment $reportedComment)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, ReportedComment $reportedComment)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ReportedComment $reportedComment)
    {
        //
    }
}
