<?php

namespace App\Http\Controllers;

use App\Models\Like;
use App\Models\Module;
use Illuminate\Http\Request;

class ModuleController extends Controller
{
    /**
     * Display a listing of the resource.
     */

    /**
     * @OA\Get(
     *      path="/modules",
     *      operationId="getModules",
     *      tags={"Modules"},
     *      summary="Get modules",
     *      description="Get modules",
     *      @OA\Response(
     *      response=200,
     *      description="Example of Success response",
     *      @OA\JsonContent(
     *
     *     @OA\Property(property="success", type="booleon", example="success"),
     *     @OA\Property(
     *                 property="data",
     *                 type="array",
     *                 example={{
     *                   "id": 1,
     *                   "title": "Mother Hood",
     *                   "thumbnail": "https://akinadev.s3.amazonaws.com/v2/images/mother-hood.jpeg",
     *                   "order": 1,
     *                   "type": "blog",
     *                   "header_content_value": "video",
     *                   "header_content_type": "dummy_url",
     *                   "created_at": "2024-05-07T01:36:40.000000Z",
     *                   "updated_at": "2024-05-07T01:36:40.000000Z"
     *                 }, {
     *                    "id": 1,
     *                    "title": "<PERSON> Hood",
     *                    "thumbnail": "https://akinadev.s3.amazonaws.com/v2/images/mother-hood.jpeg",
     *                    "order": 1,
     *                    "type": "blog",
     *                    "header_content_value": "video",
     *                    "header_content_type": "dummy_url",
     *                    "created_at": "2024-05-07T01:36:40.000000Z",
     *                    "updated_at": "2024-05-07T01:36:40.000000Z"
     *                  }},
     *                 @OA\Items(
     *
     *                 ),
     *              ),
     *
     *        )
     *     ),
     *        @OA\Response(
     *      response=401,
     *      description="Example of Bad Request",
     *      @OA\JsonContent(
     *      @OA\Property(property="message", type="string", example="Unauthenticated")
     *        )
     *     )
     * )
     *
     */
    public function index(): \Illuminate\Http\JsonResponse
    {
        $modules = Module::whereNotIn('type', ['event', 'event_new', 'coming_soon'])->with('metas')->get();

        return response()->json(
            [
                'success' => true,
                'modules' => $modules,
            ]);
    }

    public function getLikeAble(): \Illuminate\Http\JsonResponse
    {
        $modules = Module::whereIn('type', ['blog', 'podcast', 'music', 'video', 'news'])->with('metas')->get();

        return response()->json(
            [
                'success' => true,
                'modules' => $modules,
            ]);
    }

    public function getBookMarkAble(): \Illuminate\Http\JsonResponse
    {
        $modules = Module::whereIn('type', ['blog', 'podcast', 'music', 'video', 'news'])->with('metas')->get();

        return response()->json(
            [
                'success' => true,
                'modules' => $modules,
            ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(Module $module)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Module $module)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Module $module)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Module $module)
    {
        //
    }
}
