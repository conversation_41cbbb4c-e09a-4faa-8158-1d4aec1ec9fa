<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\UserCountThreshold;
use Illuminate\Http\JsonResponse;

class UserCountThresholdController extends Controller
{
    public function index(): JsonResponse
    {
        $userCountThreshold = UserCountThreshold::firstOrFail();
        $usersCount = User::count();

        return response()->json([
            'status' => 'success',
            'is_registration_allowed' => $userCountThreshold->allowed_user_count > $usersCount ,
        ]);
    }
}
