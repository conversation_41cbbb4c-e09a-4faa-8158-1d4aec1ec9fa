<?php

namespace App\Http\Controllers;

use App\Services\StripeWebhookLoggingService;
use Illuminate\Http\Request;
use <PERSON><PERSON>\Cashier\Http\Controllers\WebhookController as CashierWebhookController;

class StripeWebhookController extends CashierWebhookController
{
    protected StripeWebhookLoggingService $logger;

    public function __construct(StripeWebhookLoggingService $logger)
    {
        $this->logger = $logger;
        parent::__construct();
    }

    public function handleWebhook(Request $request)
    {
        $event = $request->all();
        $eventType = $event['type'] ?? null;


        try {
            // Log the incoming event
            $log = $this->logger->logEvent($event);

            // Process with parent Cashier handler
            $response = parent::handleWebhook($request);

            // Handle custom logic BEFORE or AFTER parent handling
            if (in_array($eventType, [
                'invoice.payment_succeeded',
                'payment_intent.succeeded',
                'customer.subscription.deleted',
                'customer.subscription.updated',
            ])) {
                $this->logger->handleUserTypeUpdate($eventType, $event);
            }

            // ✅ Add this: update next renewal + payment log
            if ($eventType === 'invoice.payment_succeeded') {
                $this->logger->handleRenewalTracking($event);
            }

            // Mark as processed if successful
            $this->logger->markAsProcessed($event['id'], 'Processed successfully');

            return $response;

        } catch (\Exception $e) {
            // Mark as failed if error occurs
            $this->logger->markAsFailed($event['id'] ?? 0, $e->getMessage());

            return response()->json(['error' => $e->getMessage()], 400);
        }
    }
}
