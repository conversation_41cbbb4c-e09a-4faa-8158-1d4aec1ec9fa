<?php

namespace App\Http\Controllers;

use App\Models\BookMark;
use App\Models\Module;
use App\Models\Music;
use App\Models\News;
use App\Models\NewsSource;
use App\Models\PodCast;
use App\Models\Post;
use App\Models\Video;
use App\Services\PostService;
use App\Traits\RestrictFreeUser;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class BookMarksController extends Controller
{
    use RestrictFreeUser;
    private PostService $postService;

    public function __construct(PostService $postService)
    {
        $this->postService = $postService;
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $module_id = $request->module_id;
        $modules = Module::whereNot('type', 'coming_soon')->get();
        $newsSources = NewsSource::all();
        $user = Auth::user();
        $user_id = $user?->id;

        $postBookmarks = Post::whereHas('bookmarks', function ($q) use ($user_id) {
            $q->where('user_id', $user_id);
        });
        $musicBookmarks = Music::whereHas('bookmarks', function ($q) use ($user_id) {
            $q->where('user_id', $user_id);
        });
        /*$newsBookmarks = News::whereHas('bookmarks', function ($q) use ($user_id) {
            $q->where('user_id', $user_id);
        });*/
        $videosBookmarks = Video::whereHas('bookmarks', function ($q) use ($user_id) {
            $q->where('user_id', $user_id);
        });
        $podCastsBookmarks = PodCast::whereHas('bookmarks', function ($q) use ($user_id) {
            $q->where('user_id', $user_id);
        });

        if ($module_id) {
            $postBookmarks = $postBookmarks->where('module_id', $module_id);;
            $musicBookmarks = $musicBookmarks->where('module_id', $module_id);
           // $newsBookmarks = $newsBookmarks->where('module_id', $module_id);
            $videosBookmarks = $videosBookmarks->where('module_id', $module_id);
            $podCastsBookmarks = $podCastsBookmarks->where('module_id', $module_id);
        }

        $podCastsBookmarks = $podCastsBookmarks->select('id', 'title', 'thumbnail', 'module_id')->get();
        $postBookmarks = $postBookmarks->select('id', 'title', 'thumbnail', 'module_id')->get();
        $musicBookmarks = $musicBookmarks->select('id', 'title', 'thumbnail', 'module_id')->get();
        $videosBookmarks = $videosBookmarks->select('id', 'title', 'thumbnail', 'module_id')->get();
        //$newsBookmarks = $newsBookmarks->select('id', 'news_source_id', 'title', 'thumbnail', 'module_id')->get();

        $allItems = $postBookmarks->mergeRecursive($musicBookmarks);
        //$allItems = $allItems->mergeRecursive($newsBookmarks);
        $allItems = $allItems->mergeRecursive($videosBookmarks);
        $allItems = $allItems->mergeRecursive($podCastsBookmarks);

        $allItems->map(function ($item) use ($modules, $newsSources) {
            $item['type'] = $modules->firstWhere('id', $item->module_id)->type;
            $item['module_slug'] = $modules->firstWhere('id', $item->module_id)->slug;
            $item['module_title'] = $modules->firstWhere('id', $item->module_id)->title;
            if ($item['type'] == 'news') {
                $item['source_name'] = $newsSources->firstWhere('id', $item->news_source_id)->name;
            }
            return $item;
        });

        return response()->json(
            [
                'success' => true,
                'bookmarks' => $allItems
            ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function bookMark(Request $request, string $module_id, string $post_id): JsonResponse
    {
        $this->restrictFree();

        $module = Module::findOrFail($module_id);
        $post = Post::findOrFail($post_id);
        $user_id = Auth::user()->id;

        if ($is_book_marked = $post->bookmarks()->where('user_id', $user_id)->first()) {
            $is_book_marked->delete();
            $status = 'unsaved';
        } else {
            $bookmark = new BookMark(['module_id' => $module->id, 'user_id' => $user_id]);
            $post->bookmarks()->save($bookmark);
            $status = 'saved';
        }

        return response()->json(
            [
                'success' => true,
                'message' => "post $status successful",
                'status' => $status,
                'post' => $this->postService->getDetail($post_id)
            ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
