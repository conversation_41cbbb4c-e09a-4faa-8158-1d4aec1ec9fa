<?php

namespace App\Http\Controllers;

use App\Http\Requests\UserNotificationPrefUpdateRequest;
use App\Models\UserNotificationPreference;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;

class UserNotificationPreferencesController extends Controller
{
    public function show(string $user_id, Request $request): \Illuminate\Http\JsonResponse
    {
        $user = Auth::user();
        if ($user->id != $user_id) {
            return response()->json(
                [
                    'success' => false,
                    'message' => 'You are not authorized to make this request.',
                ], 403);
        }
        $user_notification_preferences = $user?->notificationPreferences;
        $all_preferences = config('usernotificationpreferences');
        $response = [];

        foreach ($all_preferences as $key => $value) {
            for ($i = 0; $i < count($value); $i++) {
                $value[$i]['selected'] = false;
                if ($user_notification_preferences && $user_notification_preferences[$value[$i]['name']]) {
                    $value[$i]['selected'] = true;
                }
            }
            $response[$key] = $value;

        }

        return response()->json(
            [
                'success' => true,
                'user_notification_preferences' => $response,
            ]);
    }

    public function update(UserNotificationPrefUpdateRequest $request, string $user_id)
    {
        $user = Auth::user();
        if ($user->id != $user_id) {
            return response()->json(
                [
                    'success' => false,
                    'message' => 'You are not authorized to make this request.',
                ], 403);
        }

        $data = $request->only([
            'general_information',
            'sound',
            'vibrate',
            'app_updates',
            'bill_reminder',
            'promotion',
            'discount_available',
            'payment_request',
            'new_service_available',
            'new_tips_available',
        ]);
        $data['user_id'] = $user_id;


        $profile_data = UserNotificationPreference::updateOrCreate(
            ['user_id' => $user_id],
            $data
        )->toArray();

        $notification_data = UserNotificationPreference::where('user_id',$user_id)->first();


        return response()->json(
            [
                'success' => true,
                'message' => 'User notification preferences updated successfully',
                'user_notification_preferences' => $notification_data,
            ]);
    }
}
