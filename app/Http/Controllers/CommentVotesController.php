<?php

namespace App\Http\Controllers;

use App\Http\Requests\CommentVotePostRequest;
use App\Models\Comment;
use App\Models\CommentVote;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class CommentVotesController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(CommentVotePostRequest $request, string $module_id, string $post_id, string $comment_id): JsonResponse
    {
        $comment = Comment::findOrFail($comment_id);
        $user_id = Auth::user()->id;
        //$response = ['success' => false];

        /*if ($is_voted = $comment->votes()->where('user_id', $user_id)->first()) {
            $response['message'] = 'You have already voted on this comment';
        } else {*/
        $commentVote = new CommentVote(['vote' => $request->vote, 'comment_id' => $module_id, 'user_id' => $user_id]);
        $comment->votes()->save($commentVote);
        $response['success'] = true;
        $response['message'] = 'Comment voted successful';
        /*}*/

        return response()->json($response);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
