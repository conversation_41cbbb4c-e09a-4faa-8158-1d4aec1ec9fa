<?php

namespace App\Http\Controllers;

use App\Models\BookMark;
use App\Models\Like;
use App\Models\Module;
use App\Models\News;
use App\Models\NewsSource;
use App\Services\FetchNewsService;
use App\Services\NewsService;
use App\Traits\RestrictFreeUser;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class NewsController extends Controller
{
    use RestrictFreeUser;
    private NewsService $newsService;

    public function __construct(NewsService $newsService)
    {
        $this->newsService = $newsService;
    }

    /**
     * Display a listing of the resource.
     */
    public function index(string $module_id, string $source_id, Request $request): \Illuminate\Http\JsonResponse
    {
        $module = Module::where('type', 'news')->where('id', $module_id)->firstOrFail();

        $limit = $request->limit ?? 10;
        $news_source = NewsSource::findOrFail($source_id);
        $news = News::where('news_source_id', $source_id)->orderBy('id', 'desc')->paginate($limit);

        return response()->json(
            [
                'success' => true,
                'module' => $module,
                'news_source' => $news_source,
                'news' => $news,
            ]);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $module_id, string $news_id)
    {
        return $this->newsService->getDetail($news_id);
    }

    /**
     * Display the specified resource.
     */
    public function showWithSource(string $module_id, string $source_id, string $news_id)
    {
        return $this->newsService->getDetail($news_id);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): \Illuminate\Http\JsonResponse
    {
        $fetchNewsService = new FetchNewsService();

        $news_sources = $fetchNewsService->getNewsFromSource($request->source_id, 0);
        $data = [];

        $i = 0;
        foreach ($news_sources['data'] as $source) {

            $data[$i]['external_id'] = $source['id'];
            $data[$i]['module_id'] = $request->module_id;
            $data[$i]['news_source_id'] = $request->source_id;
            $data[$i]['title'] = $source['headline'];
            $data[$i]['description'] = $source['summary_text'];
            $data[$i]['author'] = $source['author'];
            $data[$i]['url'] = $source['article_uri'];
            $data[$i]['thumbnail'] = $source['news_image'];
            $i++;
        }

        News::upsert($data, ['external_id'], ['title']);

        return response()->json(
            [
                'success' => true,
                'message' => 'News imported successfully',
            ]);
    }

    public function like(Request $request, string $module_id, string $news_id): JsonResponse
    {
        $this->restrictFree();

        $news = News::findOrFail($news_id);
        $user_id = Auth::user()->id;

        if ($is_liked = $news->likes()->where('user_id', $user_id)->first()) {
            $is_liked->delete();
            $status = 'unlike';
        } else {
            $like = new Like(['module_id' => $module_id, 'user_id' => $user_id]);
            $news->likes()->save($like);
            $status = 'like';
        }

        return response()->json(
            [
                'success' => true,
                'message' => "News $status successful",
                'status' => $status,
                'news' => $this->newsService->getDetail($news_id),
            ]);
    }

    public function bookMark(Request $request, string $module_id, string $news_id): JsonResponse
    {
        $this->restrictFree();

        $news = News::findOrFail($news_id);
        $user_id = Auth::user()->id;

        if ($is_book_marked = $news->bookmarks()->where('user_id', $user_id)->first()) {
            $is_book_marked->delete();
            $status = 'unsaved';
        } else {
            $bookmark = new BookMark(['module_id' => $module_id, 'user_id' => $user_id]);
            $news->bookmarks()->save($bookmark);
            $status = 'saved';
        }

        return response()->json(
            [
                'success' => true,
                'message' => "News $status successful",
                'status' => $status,
                'news' => $this->newsService->getDetail($news_id),
            ]);
    }
}
