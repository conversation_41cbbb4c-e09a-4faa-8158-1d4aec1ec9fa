<?php

namespace App\Http\Controllers;

use App\Http\Requests\UserFollowPostRequest;
use App\Http\Resources\UserResource;
use App\Http\Resources\UserResourceCollection;
use App\Jobs\SendSingleUserNotificationJob;
use App\Models\User;
use App\Models\UserFollower;
use App\Services\PushNotificationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class UserFollowersController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    private PushNotificationService $pushNotificationService;

    public function __construct(PushNotificationService $pushNotificationService)
    {
        $this->pushNotificationService = $pushNotificationService;
    }

    public function index(): UserResourceCollection
    {
        $limit = $request->limit ?? 10;
        $user = Auth::user();
        $followingIdsArray = $user ? $user->followings()->pluck('user_id'): collect();

        $followerIds = UserFollower::where('user_id', $user->id)->pluck('follower_id');
        $followers = User::whereIn('id', $followerIds)->paginate($limit);

        return new UserResourceCollection($followers, $followingIdsArray);
    }

    /**
     * Display a listing of the resource.
     */
    public function otherUsersFollowers(string $user_id): UserResourceCollection
    {
        $limit = $request->limit ?? 10;
        $user = User::findOrFail($user_id);
        $followingIdsArray = Auth::user() ? Auth::user()->followings()->pluck('user_id'): collect();

        $followerIds = UserFollower::where('user_id', $user->id)->pluck('follower_id');
        $followers = User::whereIn('id', $followerIds)->paginate($limit);

        return new UserResourceCollection($followers, $followingIdsArray);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(UserFollowPostRequest $request): \Illuminate\Http\JsonResponse
    {
        $user = User::findOrFail(Auth::user()->id);

        $status = "followed";

        $requestedUser = User::findOrFail($request->user_id);

        if ($requestedUser->id == $user->id) {
            return response()->json(
                [
                    'success' => false,
                    'message' => "User cannot follow itself",
                ], 403);
        }

        $isFollower = UserFollower::where('follower_id', $user->id)->where('user_id', $request->user_id)->first();
        if ($isFollower) {
            $status = "un_followed";
            $isFollower->delete();
        } else {
            $userFollower = new UserFollower();
            $userFollower->user_id = $request->user_id;
            $userFollower->follower_id = $user->id;
            $userFollower->save();

            $this->sendFollowNotification($requestedUser, $user);
        }
        $followingIdsArray = $user ? $user->followings()->pluck('user_id'): collect();

        return response()->json(
            [
                'success' => true,
                'message' => "User $status successfully",
                'status' => $status,
                'user' => new UserResource($requestedUser, $followingIdsArray),
            ]);
    }

    private function sendFollowNotification(User $requestedUser, User $follower)
    {
        $followerFullName = "{$follower->first_name} {$follower->last_name}";

        SendSingleUserNotificationJob::dispatch(
            $requestedUser->id,
            "{$followerFullName} started following you",
            "{$followerFullName} is now following you on AkinaConnect",
            [
                'follower_id' => (string)$follower->id,
                'url' => "akinaconnect://user-profile/{$follower->id}",
                'web_url' => "/social-connect/user/{$follower->id}",
            ]
        );
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
