<?php

namespace App\Http\Controllers;

use App\Http\Requests\CommentReplyPatchRequest;
use App\Http\Requests\CommentReplyPostRequest;
use App\Jobs\SendSingleUserNotificationJob;
use App\Models\Comment;
use App\Models\CommentReply;
use App\Services\PushNotificationService;
use App\Services\SocialConnectPostService;
use App\Traits\RestrictFreeUser;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class SocialConnectPostCommentRepliesController extends Controller
{
    use RestrictFreeUser;
    private SocialConnectPostService $socialConnectPostService;
    private PushNotificationService $pushNotificationService;

    public function __construct(SocialConnectPostService $socialConnectPostService, PushNotificationService $pushNotificationService)
    {
        $this->socialConnectPostService = $socialConnectPostService;
        $this->pushNotificationService = $pushNotificationService;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(CommentReplyPostRequest $request, string $post_id, string $comment_id): JsonResponse
    {
        $this->restrictFree();

        $comment = Comment::findOrFail($comment_id);
        $user_id = Auth::user()->id;

        $commentReply = new CommentReply(['reply' => $request->reply, 'comment_id' => $comment_id, 'user_id' => $user_id]);
        $reply = $comment->replies()->save($commentReply);

        $this->notifyUsersAboutReply($comment, $reply, $request->reply);

        return response()->json(
            [
                'success' => true,
                'message' => "Comment reply added successful",
                'reply' => $reply,
                'post' => $this->socialConnectPostService->getDetail($post_id)
            ]);
    }

    private function notifyUsersAboutReply(Comment $comment, CommentReply $reply, string $replyText)
    {
        $post = $comment->commentable;
        $user_id = Auth::user()->id;
        $repliedBy = Auth::user();
        $replierFullName = "{$repliedBy->first_name} {$repliedBy->last_name}";

        // Notify the comment owner
        if ($comment->user_id !== $user_id) {
            $commentOwner = $comment->user;

            SendSingleUserNotificationJob::dispatch(
              $commentOwner->id,
              "{$replierFullName} replied to your comment",
              "{$replierFullName} replied: \"" . Str::limit($replyText, 20) . "\"",
              [
                  'post_id' => (string)$post->id,
                  'comment_id' => (string)$comment->id,
                  'reply_id' => (string)$reply->id,
                  'url' => "akinaconnect://social-connect-detail/{$post->id}",
                  'web_url' => "/social-connect/post/{$post->id}",
              ]
            );
        }

        // Notify the post owner (if not the same as the comment owner or reply author)
        if ($post->user_id !== $user_id && $post->user_id !== $comment->user_id) {
            $postOwner = $post->user;

            SendSingleUserNotificationJob::dispatch(
                $postOwner->id,
                "{$replierFullName} replied to a comment on your post",
                "{$replierFullName} replied: \"{$replyText}\"",
                [
                    'post_id' => (string)$post->id,
                    'comment_id' => (string)$comment->id,
                    'reply_id' => (string)$reply->id,
                    'url' => "akinaconnect://social-connect-detail/{$post->id}"
                ]
            );
        }
    }


    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(CommentReplyPatchRequest $request, string $post_id, string $comment_id, string $reply_id)
    {
        $this->restrictFree();

        $user_id = Auth::user()->id;
        $commentReply = CommentReply::findOrFail($reply_id);

        if ($commentReply->user_id !== $user_id) {
            return response()->json(
                [
                    'success' => false,
                    'message' => "You are not allowed to update this comment reply",
                ], 403);
        }

        $commentReply->reply = $request->reply;
        $commentReply->save();

        return response()->json(
            [
                'success' => true,
                'message' => "Comment reply updated successful",
                'reply' => $commentReply,
                'post' => $this->socialConnectPostService->getDetail($post_id)
            ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $post_id, string $comment_id, string $reply_id)
    {
        $user_id = Auth::user()->id;
        $commentReply = CommentReply::findOrFail($reply_id);

        if ($commentReply->user_id !== $user_id) {
            return response()->json(
                [
                    'success' => false,
                    'message' => "You are not allowed to delete this comment reply",
                ], 403);
        }

        $commentReply->delete();

        return response()->json(
            [
                'success' => true,
                'message' => "Comment reply deleted successful",
                'post' => $this->socialConnectPostService->getDetail($post_id)
            ]);
    }
}
