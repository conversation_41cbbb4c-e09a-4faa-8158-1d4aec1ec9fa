<?php

namespace App\Http\Controllers;

use App\Http\Requests\FCMTokenPostRequest;
use App\Jobs\SendSingleUserNotificationJob;
use App\Models\FCMToken;
use App\Models\UserNotification;
use App\Services\PushNotificationService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class UserNotificationsController extends Controller
{
    protected PushNotificationService $pushNotificationService;

    public function __construct(PushNotificationService $pushNotificationService)
    {
        $this->pushNotificationService = $pushNotificationService;
    }

    public function index(): JsonResponse
    {
        $limit = $request->limit ?? 10;
        $user_id = Auth::user()->id;
        $notifications = UserNotification::where('user_id', $user_id)
            ->orWhere('user_id', null)
            ->orderBy('created_at', 'desc')->paginate($limit);

        UserNotification::where('user_id', $user_id)->update(['is_read' => true]);

        return response()->json($notifications);
    }

    public function markAsRead(string $notification_id): JsonResponse
    {
        $user_id = Auth::user()->id;
        $notification = UserNotification::where('id', $notification_id)->firstOrFail();
        $notification->is_read = true;
        $notification->save();

        $notifications_count = $this->pushNotificationService->unreadNotificationsCount($user_id);

        return response()->json([
            'success' => true,
            'notifications_count' => $notifications_count,
            'message' => 'Notification marked as read.'
        ]);
    }

    public function registerDevice(FCMTokenPostRequest $request)
    {
        $user = Auth::user();

        $subscription_response = $this->pushNotificationService->registerToken($user, $request->client_platform_os, $request->device_token);

        return response()->json([
            'success' => true,
            'message' => 'Device registered and subscribed to topic successfully.',
            'subscription_response' => $subscription_response
        ]);
    }

    public function sendNotification(Request $request)
    {
        $tokens = FCMToken::where('user_id', $request->user_id)->pluck('device_token')->toArray();

        $notification = UserNotification::create([
            'user_id' => $request->user_id,
            'title' => $request->title,
            'body' => $request->body,
            'data' => $request->data,
        ]);

        if (empty($tokens)) {
            return response()->json(['success' => false, 'message' => 'No device tokens found for the user.'], 404);
        }

        SendSingleUserNotificationJob::dispatch(
            $request->user_id,
            $request->title,
            $request->body,
            $request->data,
        );

        return response()->json([
            'success' => true,
            'notification' => $notification
        ]);
    }

}
