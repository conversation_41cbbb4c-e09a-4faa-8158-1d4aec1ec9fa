<?php

namespace App\Http\Controllers;

use App\Enums\PostStatus;
use App\Models\Category;
use App\Models\Module;
use App\Models\Post;
use App\Services\PostService;
use App\Services\PushNotificationService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class PostsController extends Controller
{
    private PostService $postService;
    protected PushNotificationService $pushNotificationService;

    public function __construct(PostService $postService, PushNotificationService $pushNotificationService)
    {
        $this->postService = $postService;
        $this->pushNotificationService = $pushNotificationService;
    }

    /**
     * Display a listing of the resource.
     */
    public function index(string $module_id, Request $request): JsonResponse
    {
        $isWeb = $request->query('request_source') === 'web';

        $module = Module::with('metas')->where('id', $module_id)->firstOrFail();

        $responseArr = ['success' => true, 'module' => $module];
        if ($isWeb) {
            $responseArr['categories'] = Category::where('module_id', $module_id)->with('posts', function ($query) {
                $query->where('status', PostStatus::Published)->with('categories')->orderBy('id', 'DESC');
            })->whereHas('posts')->orderBy('id', 'ASC')->get();
        } else {
            $responseArr['posts'] = Post::where('module_id', $module_id)->with('categories')->orderBy('id', 'DESC')->get();

        }

        return response()->json($responseArr);
    }

    public function getByCategory(string $category_id, Request $request): JsonResponse
    {
        $posts = Post::whereHas('categories', function ($q) use ($category_id) {
            $q->where('category_id', $category_id);
        })->with('categories')->withCount('comments')->orderBy('id', 'DESC')->get();

        return response()->json([
            'success' => true,
            'posts' => $posts
        ]);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $module_id, string $post_id) : \App\Http\Resources\PostResource
    {
        return $this->postService->getDetail($post_id);
    }
}
