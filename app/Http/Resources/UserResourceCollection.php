<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\PaginatedResourceResponse;
use Illuminate\Http\Resources\Json\ResourceCollection;
use Illuminate\Support\Facades\Auth;

class UserResourceCollection extends ResourceCollection
{
    protected $authUserFollowingIds;

    public function __construct($resource, $authUserFollowingIds = null)
    {
        parent::__construct($resource);
        $this->authUserFollowingIds = $authUserFollowingIds ?: collect();
    }

    public function toArray($request)
    {
        return [
            'data' => $this->collection->map(function ($user) {
                return new UserResource($user, $this->authUserFollowingIds);
            }),
        ];
    }

    public function withPagination($request)
    {
        return (new PaginatedResourceResponse($this))->toResponse($request)->getData(true);
    }

    public function toResponse($request)
    {
        $pagination = $this->withPagination($request);

        return response()->json(array_merge(
            $this->toArray($request),
            $pagination['meta']
        ));
    }
}
