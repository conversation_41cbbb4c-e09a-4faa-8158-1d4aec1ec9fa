<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SocialConnectPostResource extends JsonResource
{
    protected $authUserFollowingIds;

    public function __construct($resource, $authUserFollowingIds = null)
    {
        parent::__construct($resource);

        $this->authUserFollowingIds = collect($authUserFollowingIds ?? []);
    }

    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'module_id' => $this->module_id,
            'type' => $this->type,
            'text' => $this->text,
            'media_link' => $this->media_link,
            'video_thumbnail' => $this->video_thumbnail,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'is_liked' => count($this->likes) > 0,
            'is_bookmarked' => count($this->bookmarks) > 0,
            'user' => $this->whenLoaded('user', function () {
                return new UserResource($this->user, $this->authUserFollowingIds);
            }),
            'reported_exists' => $this->whenExistsLoaded('reported_exists'),
            'comments' => $this->whenLoaded('comments'),
            'comments_count' => $this->whenCounted('comments_count'),
            'likes_count' => $this->whenCounted('likes_count'),
            'bookmarks_count' => $this->whenCounted('bookmarks_count'),
        ];
    }
}
