<?php

namespace App\Http\Resources;

use App\Enums\PostStatus;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class PostResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'module_id' => $this->module_id,
            'title' => $this->title,
            'content' => $this->content,
            'thumbnail' => $this->thumbnail,
            'status' => PostStatus::tryFrom($this->status)->name,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'is_liked' => count($this->likes) > 0,
            'is_bookmarked' => count($this->bookmarks) > 0,
            'categories' => $this->categories,
            'comments' => $this->whenLoaded('comments'),
        ];
    }
}
