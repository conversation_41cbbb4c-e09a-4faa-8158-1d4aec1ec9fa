<?php

namespace App\Http\Resources;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Auth;

class UserResource extends JsonResource
{
    protected $authUserFollowingIds;

    public function __construct($resource, $authUserFollowingIds = null)
    {
        parent::__construct($resource);

        $this->authUserFollowingIds = collect($authUserFollowingIds ?? []);
    }

    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'first_name' => $this->first_name,
            'last_name' => $this->last_name,
            'email' => $this->email,
            'user_name' => $this->user_name,
            'profile_photo' => $this->profile_photo,
            'is_followed' => $this->authUserFollowingIds->contains($this->id),
        ];
    }

    public static function collection($resource, $authUserFollowingIds = null)
    {
        // Ensure we pass a collection to the ResourceCollection
        $followingIds = collect($authUserFollowingIds ?? []);
        return new UserResourceCollection($resource, $followingIds);
    }
}
