<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class MusicResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'audio_link' => $this->audio_link,
            'thumbnail' => $this->thumbnail,
            'previous' => $this->previous_record?->id,
            'next' => $this->next_record?->id,
            'category_id' => $this->category_id,
            'module_id' => (int) $this->module_id,
            'tag_id' => $this->tag_id,
            'is_liked' => count($this->likes) > 0,
            'is_bookmarked' => count($this->bookmarks) > 0,
            'comments' => $this->whenLoaded('comments'),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
