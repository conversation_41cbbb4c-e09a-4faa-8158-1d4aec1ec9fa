<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Http\Resources\Json\PaginatedResourceResponse;
use Illuminate\Http\Resources\Json\ResourceCollection;

class SocialConnectPostResourceCollection extends ResourceCollection
{
    public function toArray($request)
    {
        return [
            'data' =>  SocialConnectPostResource::collection($this->collection), // This returns your resource data
        ];
    }

    public function withPagination($request)
    {
        return (new PaginatedResourceResponse($this))->toResponse($request)->getData(true);
    }

    public function toResponse($request)
    {
        $pagination = $this->withPagination($request);

        return response()->json(array_merge([
            'data' => $this->collection,
        ], $pagination['meta']));  // This merges the meta into the root
    }
}
