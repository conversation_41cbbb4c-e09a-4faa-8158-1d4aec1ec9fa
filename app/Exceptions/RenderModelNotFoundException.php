<?php

namespace App\Exceptions;

use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Throwable;

class RenderModelNotFoundException
{
    public function __invoke(Throwable $e)
    {
        if ($e instanceof ModelNotFoundException) {
            return $this->jsonResponse($e);
        }

        if ($e instanceof NotFoundHttpException && $e->getPrevious() instanceof ModelNotFoundException) {
            return $this->jsonResponse($e->getPrevious());
        }

        return null;
    }

    protected function jsonResponse(ModelNotFoundException $e)
    {
        return response()->json([
            'status' => false,
            'message' => class_basename($e->getModel()) . ' not found.'
        ], Response::HTTP_NOT_FOUND);
    }
}
