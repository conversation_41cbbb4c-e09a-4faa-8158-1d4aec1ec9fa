<?php

namespace App\Traits;

use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Log;

trait Encryptable
{
    public function setAttribute($key, $value)
    {
        if (in_array($key, $this->encryptable ?? [])) {
            $value = Crypt::encryptString($value);
        }

        return parent::setAttribute($key, $value);
    }

    public function getAttributeValue($key)
    {
        $value = parent::getAttributeValue($key);

        if (in_array($key, $this->encryptable ?? []) && !is_null($value)) {
            try {
                return Crypt::decryptString($value);
            } catch (\Exception $e) {
                // Optional: log or handle corrupted/malformed data
                return $value; // fallback to raw value
            }
        }

        return $value;
    }

    public function attributesToArray()
    {
        $attributes = parent::attributesToArray();

        foreach ($this->encryptable ?? [] as $field) {
            if (array_key_exists($field, $attributes) && !is_null($this->{$field})) {
                $attributes[$field] = $this->{$field}; // triggers decryption
            }
        }

        return $attributes;
    }
}
