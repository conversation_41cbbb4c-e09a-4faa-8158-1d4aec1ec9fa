<?php

namespace App\Traits;

use App\Enums\UserType;
use Illuminate\Http\Exceptions\HttpResponseException;

trait RestrictFreeUser
{
    public function restrictFree(): void
    {
        if (auth()->check() && auth()->user()?->user_type === UserType::FREE->value) {
            throw new HttpResponseException(
                response()->json(['message' => 'Action not allowed for free users.'], 403)
            );
        }
    }
}
