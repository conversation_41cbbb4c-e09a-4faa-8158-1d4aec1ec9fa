<?php

namespace App\Traits;

use App\Enums\UserType;
use App\Models\ApiRequestLimit;
use Illuminate\Http\Exceptions\HttpResponseException;
use Carbon\Carbon;

trait WeeklyRequestLimiter
{
    public function checkAndIncrementRequestLimit(string $action = 'akina_ai', int $limit = 10)
    {
        $user = auth()->user();
        if ($user?->user_type === UserType::PREMIUM->value) {
            return $limit;
        }

        $weekStart = Carbon::now()->startOfWeek(Carbon::MONDAY)->toDateString();

        $record = ApiRequestLimit::firstOrNew([
            'user_id' => $user->id,
            'action' => $action,
            'week_start' => $weekStart,
        ]);
        $record->count = $record->count ?? $limit;

        if ($record->count <= 0) {
            throw new HttpResponseException(
                response()->json(['message' => 'Weekly request limit reached for this action.'], 429)
            );
        }

        $record->count -= 1;
        $record->save();

        return $record->count;
    }

    public function checkRequestLimit(string $action = 'akina_ai', int $limit = 10): int
    {
        $user = auth()->user();

        if ($user?->user_type === UserType::PREMIUM->value) {
            return $limit;
        }

        $weekStart = Carbon::now()->startOfWeek(Carbon::MONDAY)->toDateString();

        $record = ApiRequestLimit::where('user_id', $user->id)
            ->where('week_start', $weekStart)
            ->first();

        return $record ? $record->count : $limit;
    }
}
