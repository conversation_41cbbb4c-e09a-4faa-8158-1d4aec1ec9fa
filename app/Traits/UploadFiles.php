<?php

namespace App\Traits;

use Illuminate\Support\Str;

trait UploadFiles
{
    public function uploadFileToS3($file, string $folder, string $prefix): string
    {
        $extension = $file->getClientOriginalExtension();
        $filename = time() . '_' . Str::slug($prefix) . '.' . $extension;

        $path = $file->storeAs($folder, $filename, 's3');

        return config('filesystems.disks.s3.url') . $path;
    }
}
