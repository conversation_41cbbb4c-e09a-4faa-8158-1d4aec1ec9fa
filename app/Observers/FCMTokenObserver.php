<?php

namespace App\Observers;

use App\Models\FCMToken;
use App\Models\FCMTokenListUser;

class FCMTokenObserver
{
    /**
     * Handle the FCMToken "created" event.
     */
    public function created(FCMToken $fCMToken): void
    {
        //
    }

    /**
     * Handle the FCMToken "updated" event.
     */
    public function updated(FCMToken $fCMToken): void
    {
        //
    }

    /**
     * Handle the FCMToken "deleted" event.
     */
    public function deleted(FCMToken $fCMToken): void
    {
        FCMTokenListUser::where('fcm_token_id', $fCMToken->id)
            ->where('user_id', $fCMToken->user_id)
            ->delete();
    }

    /**
     * Handle the FCMToken "restored" event.
     */
    public function restored(FCMToken $fCMToken): void
    {
        //
    }

    /**
     * Handle the FCMToken "force deleted" event.
     */
    public function forceDeleted(FCMToken $fCMToken): void
    {
        //
    }
}
