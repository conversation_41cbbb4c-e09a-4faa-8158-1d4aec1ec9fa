<?php

namespace App\Services;

use App\Http\Resources\MusicResource;
use App\Http\Resources\PostResource;
use App\Models\BlockedUser;
use App\Models\Music;
use App\Models\Post;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Http;

class MusicService
{
    public function getDetail(string $music_id, string $module_id): MusicResource
    {
        $user_id = Auth::user()->id ?? 0;

        $blockedUserIds = collect();
        if ($user_id > 0) {
            $blockedUserIds = BlockedUser::where('blocked_by', $user_id)
                ->pluck('user_id');
        }

        $music = Music::with(['likes' => function ($q) use ($user_id) {
            $q->where('user_id', $user_id);
        }])
            ->with(['bookmarks' => function ($q) use ($user_id) {
                $q->where('user_id', $user_id);
            }])
            ->when($user_id, function ($q) use ($blockedUserIds, $user_id) {
                $q->with([
                    'comments' => function ($q) use ($blockedUserIds, $user_id) {
                        $q->whereNotIn('user_id', $blockedUserIds)
                            ->withExists(['reportedComment' => function ($q2) use ($user_id) {
                                $q2->where('reported_by', $user_id);
                            }])
                            ->with('user');
                        $q->with([
                            'replies' => function ($q) use ($blockedUserIds, $user_id) {
                                $q->whereNotIn('user_id', $blockedUserIds)
                                    ->withExists(['reportedReply' => function ($q2) use ($user_id) {
                                        $q2->where('reported_by', $user_id);
                                    }])
                                    ->with('user');

                            }
                        ]);
                    }
                ]);
            })
            ->findOrFail($music_id);


        $music['previous_record'] = Music::where('id', '<', $music->id)->where('category_id', $music->category_id)->orderBy('id', 'desc')->select('id')->first();

        $music['next_record'] = Music::where('id', '>', $music->id)->where('category_id', $music->category_id)->orderBy('id')->select('id')->first();
        $music['module_id'] = $module_id;


        return new MusicResource($music);
    }
}
