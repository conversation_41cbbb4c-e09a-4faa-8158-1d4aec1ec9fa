<?php

namespace App\Services;

use App\Enums\UserMembershipType;
use App\Models\AffiliateCode;
use App\Models\User;
use Carbon\Carbon;

class AffiliateCodeService
{
    protected MembershipService $membershipService;
    public function __construct(MembershipService $membershipService)
    {
        $this->membershipService = $membershipService;
    }

    /**
     * Handle the affiliate code for a user.
     *
     * @param User $user
     * @return AffiliateCode|null
     */
    public function handleAffiliateCode(User $user): ?AffiliateCode
    {
        if (!$user->affiliate_code) {
            return null;
        }

        $affiliateCode = AffiliateCode::where('is_active', true)
            ->whereNull('redeemed_by')
            ->where('code', $user->affiliate_code)
            ->first();

        if (!$affiliateCode) {
            return null;
        }

        $affiliateCode->redeemed_by = $user->id;
        $affiliateCode->redeemed_at = Carbon::now();
        $affiliateCode->save();


        $this->membershipService->makeUserMember($user,UserMembershipType::AFFILIATE_CDOE->value);

        return $affiliateCode;
    }
}
