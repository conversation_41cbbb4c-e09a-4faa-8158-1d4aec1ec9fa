<?php

namespace App\Services;

use App\Http\Resources\VideoResource;
use App\Models\BlockedUser;
use App\Models\Video;
use Illuminate\Support\Facades\Auth;

class VideoService
{
    public function getDetail($video_id, $module_id): VideoResource
    {
        $user_id = Auth::user()->id ?? 0;


        $blockedUserIds = collect();
        if ($user_id > 0) {
            $blockedUserIds = BlockedUser::where('blocked_by', $user_id)
                ->pluck('user_id');
        }

        $video = Video::with(['likes' => function ($q) use ($user_id) {
            $q->where('user_id', $user_id);
        }])->with(['bookmarks' => function ($q) use ($user_id) {
            $q->where('user_id', $user_id);
        }])
            ->when($user_id, function ($q) use ($blockedUserIds, $user_id) {
                $q->with([
                    'comments' => function ($q) use ($blockedUserIds, $user_id) {
                        $q->whereNotIn('user_id', $blockedUserIds)
                            ->withExists(['reportedComment' => function ($q2) use ($user_id) {
                                $q2->where('reported_by', $user_id);
                            }])
                            ->with('user');
                        $q->with([
                            'replies' => function ($q) use ($blockedUserIds, $user_id) {
                                $q->whereNotIn('user_id', $blockedUserIds)
                                    ->withExists(['reportedReply' => function ($q2) use ($user_id) {
                                        $q2->where('reported_by', $user_id);
                                    }])
                                    ->with('user');

                            }
                        ]);
                    }
                ]);
            })
            ->findOrFail($video_id);


        $video['previous_record'] = Video::where('id', '<', $video->id)->where('category_id', $video->category_id)->orderBy('id', 'desc')->select('id')->first();

        $video['next_record'] = Video::where('id', '>', $video->id)->where('category_id', $video->category_id)->orderBy('id')->select('id')->first();
        $video['module_id'] = $module_id;


        return new VideoResource($video);
    }
}
