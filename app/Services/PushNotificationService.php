<?php

namespace App\Services;

use App\Enums\UserType;
use App\Models\FCMToken;
use App\Models\FcmTokenList;
use App\Models\FCMTokenListUser;
use App\Models\User;
use App\Models\UserNotification;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Kreait\Firebase\Factory;
use Kreait\Firebase\Messaging\ApnsConfig;
use Kreait\Firebase\Messaging\CloudMessage;

class PushNotificationService
{
    protected $messaging;

    public function __construct()
    {
        $firebase = (new Factory)->withServiceAccount(config('firebase.projects.app.credentials'));
        $this->messaging = $firebase->createMessaging();
    }

    public function getMessaging()
    {
        return $this->messaging;
    }

    public function sendFirebaseMessage(array $tokens, string $title, string $body, array $data = [])
    {
        $notification = [
            'title' => $title,
            'body' => $body,
        ];

        if (array_key_exists('thumbnail', $data) && !empty($data['thumbnail'])) {
            $notification['image'] = $data['thumbnail'];
        }

        $badgeCount = array_key_exists('unread_count', $data) ? (int)$data['unread_count'] : 0;

        $apnsConfig = ApnsConfig::new()
            ->withApsField('badge', $badgeCount);

        $message = CloudMessage::new()
            ->withNotification($notification)
            ->withApnsConfig($apnsConfig)
            ->withData($data);


        return $this->messaging->sendMulticast($message, $tokens);
    }

    public function sendNotification(string $user_id, string $title, string $body, array $data = [])
    {
        $tokens = FCMToken::where('user_id', $user_id)->pluck('device_token')->toArray();

        $notification = UserNotification::create([
            'user_id' => $user_id,
            'title' => $title,
            'body' => $body,
            'data' => $data,
        ]);

        if (empty($tokens)) {
            return [
                'success' => false,
                'message' => 'No device tokens found for the user.',
            ];
        }

        $this->sendFirebaseMessage($tokens, $title, $body, $data);
        $notification->is_sent = true;
        $notification->save();

        return $notification;
    }

    public function sendNotificationToTopic(string $topic, string $title, string $body, array $data = [], $broadCastMessageId = null): void
    {
        $batchId = $this->saveTopicNotifications($topic, $title, $body, $data, $broadCastMessageId);

        $message = CloudMessage::fromArray([
            'topic' => $topic,
            'notification' => [
                'title' => $title,
                'body' => $body,
            ],
            'data' => $data
        ]);

        $this->messaging->send($message);

        $this->updateSentStatus($batchId);
    }

    private function updateSentStatus($batchId): void
    {
        UserNotification::where('batch_id', $batchId)->update(['is_sent' => true]);
    }

    public function saveNotification(string $user_id, string $title, string $body, array $data = []): UserNotification
    {
        return UserNotification::create([
            'user_id' => $user_id,
            'title' => $title,
            'body' => $body,
            'data' => $data,
        ]);
    }

    private function saveTopicNotifications(string $topic, string $title, string $body, array $data = [], int $broadCastMessageId = null): string
    {
        if ($this->isDefaultTopic($topic)) {
            $userIds = User::pluck('id');
        } else if ($topic === config('firebase.free_user_topic')) {
            $userIds = User::where('user_type', UserType::FREE->value)->pluck('id');
        } else if ($topic === config('firebase.premium_user_topic')) {
            $userIds = User::where('user_type', UserType::PREMIUM->value)->pluck('id');
        } else {
            $listId = FcmTokenList::where('name', $topic)->value('id');
            $userIds = FCMTokenListUser::where('fcm_token_list_id', $listId)
                ->distinct()
                ->pluck('user_id');
        }

        $batchId = Str::uuid();
        $now = now();
        $notifications = [];

        foreach ($userIds as $userId) {
            $notifications[] = [
                'user_id' => $userId,
                'title' => $title,
                'body' => $body,
                'data' => json_encode($data),
                'admin_broadcast_message_id' => $broadCastMessageId,
                'is_sent' => false,
                'batch_id' => $batchId, // Assign batch ID
                'created_at' => $now,
                'updated_at' => $now,
            ];
        }

        // Bulk insert notifications
        DB::table('user_notifications')->insert($notifications);

        // Retrieve only the inserted IDs using the batch ID
        $notificationIds = UserNotification::where('batch_id', $batchId)
            ->pluck('id')
            ->toArray();

        return $batchId;
    }

    private function isDefaultTopic($topic): bool
    {
        return $topic === config('firebase.default_topic');
    }

    /**
     * @param $user_id
     * @return int
     */
    public function unreadNotificationsCount($user_id = null): int
    {
        $current_user_id = $user_id ?? Auth::user()->id;
        return UserNotification::where('user_id', $current_user_id)->where('is_read', false)->count();
    }

    public function registerToken(User $user, string $platform, string $token): array
    {
        $this->deleteTokenRecordBeforeCreatingNew($user, $platform);

        $tokenRow = FCMToken::create([
            'user_id' => $user->id,
            'device_token' => $token,
            'device_type' => $platform
        ]);

        $topics = [config('firebase.default_topic')];
        if ($user->user_type === UserType::FREE->value) {
            $topics[] = config('firebase.free_user_topic');
        } elseif ($user->user_type === UserType::PREMIUM->value) {
            $topics[] = config('firebase.premium_user_topic');
        }

        foreach ($topics as $topic) {
            $list = FcmTokenList::firstOrCreate(['name' => $topic]);
            $listData = [
                'fcm_token_list_id' => $list->id,
                'user_id' => $user->id,
                'fcm_token_id' => $tokenRow->id,
            ];

            FCMTokenListUser::create($listData);
        }

        return $this->getMessaging()->subscribeToTopics($topics, $token);
    }

    public function syncTopicTokens(string $fcmTokenListId, array $userIds): array
    {
        if (empty($userIds)) {
            throw new \InvalidArgumentException('User IDs array cannot be empty.');
        }

        $fcmTokenList = FcmTokenList::findOrFail($fcmTokenListId);
        $now = now();

        $fcmTokens = FCMToken::whereIn('user_id', $userIds)->get(['id', 'user_id', 'device_token']);

        $syncData = $fcmTokens->mapWithKeys(function ($token) use ($now) {
            return [
                $token->id => [
                    'user_id' => $token->user_id,
                    'created_at' => $now,
                    'updated_at' => $now,
                ]
            ];
        })->toArray();

        $deviceTokens = $fcmTokens->pluck('device_token')->all();
        if (!empty($tokens)) {
            $this->getMessaging()->subscribeToTopic($fcmTokenList->name, $deviceTokens);
        }

        return $fcmTokenList->tokens()->sync($syncData);
    }

    public function subscribeToTopic(User $user, string $topic): array
    {
        $list = FcmTokenList::firstOrCreate(['name' => $topic]);
        $fcmTokenListUserQuery = FCMTokenListUser::where('fcm_token_list_id', $list->id)
            ->where('user_id', $user->id)
            ->with('tokens');

        $fcmTokenListUser = $fcmTokenListUserQuery->get();

        if ($fcmTokenListUser->isNotEmpty()) {
            return [];
        } else {
            $fcmTokens = FCMToken::where('user_id', $user->id)
                ->get(['id', 'user_id', 'device_token']);

            $now = now();
            $listData = [];
            foreach ($fcmTokens as $fcmToken) {
                $listData[] = [
                    'fcm_token_list_id' => $list->id,
                    'user_id' => $user->id,
                    'fcm_token_id' => $fcmToken->id,
                    'created_at' => $now,
                    'updated_at' => $now,
                ];
            }
            if (empty($listData)) return [];

            FCMTokenListUser::insert($listData);

            $deviceTokens = $fcmTokens->pluck('device_token')->all();

            return !empty($tokens) ? $this->getMessaging()->subscribeToTopic($topic, $deviceTokens) : [];
        }
    }

    public function unSubscribeFromTopic(User $user, string $topic): array
    {
        $list = FcmTokenList::firstOrCreate(['name' => $topic]);
        $fcmTokenListUserQuery = FCMTokenListUser::where('fcm_token_list_id', $list->id)
            ->where('user_id', $user->id)
            ->with('tokens');

        $fcmTokenListUser = $fcmTokenListUserQuery->get();

        if ($fcmTokenListUser->isNotEmpty()) {
            $deviceTokens = $fcmTokenListUser
                ->pluck('tokens.device_token')
                ->filter()
                ->toArray();

            $fcmTokenListUserQuery->delete();

            return !empty($tokens) ? $this->getMessaging()->unsubscribeFromTopic($topic, $deviceTokens) : [];
        } else {
            return []; // No tokens to unsubscribe
        }
    }

    public function unSubscribeFromAllTopics(User $user, string|null $platform = null): array
    {
        $fcmTokens = FCMToken::where('user_id', $user->id)
            ->when($platform, function ($query) use ($platform) {
                return $query->where('device_type', $platform);
            })
            ->get(['id', 'user_id', 'device_token']);


        $tokenIds = $fcmTokens->pluck('id')->all();
        FCMTokenListUser::where('user_id', $user->id)->whereIn('fcm_token_id', $tokenIds)->delete();

        $tokens = $fcmTokens->pluck('device_token')->all();

        return !empty($tokens) ? $this->getMessaging()->unsubscribeFromAllTopics($tokens) : [];
    }

    private function deleteTokenRecordBeforeCreatingNew(User $user, string $platform): ?bool
    {
        $this->unSubscribeFromAllTopics($user, $platform);

        return FCMToken::where('user_id', $user->id)
            ->where('device_type', $platform)
            ->delete();
    }

    public function deactivateToken(User $user, string $platform): bool
    {
        $this->unSubscribeFromAllTopics($user, $platform);

        return FCMToken::where('device_type', $platform)->where('user_id', $user->id)->delete();
    }

    public function getUserTokens(string $user_id): array
    {
        return FCMToken::where('user_id', $user_id)->pluck('device_token')->toArray();
    }
}

