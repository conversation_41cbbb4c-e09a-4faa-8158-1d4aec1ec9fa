<?php

namespace App\Services;

use App\Enums\UserMembershipType;
use App\Enums\UserType;
use App\Models\AffiliateCode;
use App\Models\ReferralCode;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Str;

class ReferralCodeService
{
    /**
     * Handle referral code for a user.
     *
     * @param string|null $code
     * @return int|null
     */
    public function handleReferralCode(string|null $code): null|int
    {
        if (!$code) {
            return null;
        }
        $referralCode = ReferralCode::where('code', $code)->whereNotNull('user_id')->first();

        return $referralCode ? $referralCode->id : null;
    }

    /**
     * Store referral code for a user.
     *
     * @param User $user
     * @return string
     */
    public function generateReferralCode(User $user): string
    {
        $base = Str::upper(Str::slug(Str::substr($user->first_name, 0, 1) . Str::substr($user->last_name, 0, 1), ''));

        $suffix = Str::upper(Str::random(6)); // or use a hashid for more predictability

        return $base . $suffix;
    }

    public function getUserShareAbleReferralCode(User $user): string|null
    {
        $referralCode = ReferralCode::where('user_id', $user->id)
            ->where('is_active', true)
            ->first();

        return $referralCode?->code ?? null;
    }
}
