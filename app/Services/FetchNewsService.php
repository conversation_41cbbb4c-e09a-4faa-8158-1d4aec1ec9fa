<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;

class FetchNewsService
{
    protected $client;

    public function __construct()
    {
        $this->client = Http::baseUrl(config('news.api_url'));
        $this->client->withToken(config('news.api_key'));
    }

    public function getNewsFromSource($source, $limit,$page,$timestamp)
    {
        $get_news_endpoint = 'news-posts';

        $http_request = $this->client->get($get_news_endpoint, [
            'source' => $source,
            'limit' => $limit,
            'page' => $page,
            'timestamp' => $timestamp,
        ]);

        return $http_request->json();
    }

    public function getNewsSource()
    {
        $get_news_endpoint = 'news-sources';

        $http_request = $this->client->get($get_news_endpoint);

        return $http_request->json();
    }
}
