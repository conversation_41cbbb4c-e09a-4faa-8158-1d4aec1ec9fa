<?php

namespace App\Services;

use App\Enums\NotificationType;
use App\Http\Resources\SocialConnectPostResource;
use App\Jobs\SendSingleUserNotificationJob;
use App\Models\BlockedUser;
use App\Models\Comment;
use App\Models\SocialConnectPost;
use App\Models\User;
use App\Models\UserFollower;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class SocialConnectPostService
{
    public function getDetail($post_id): SocialConnectPostResource
    {
        $user_id = Auth::user()->id ?? 0;

        $blockedUserIds = collect();
        if ($user_id > 0) {
            $blockedUserIds = BlockedUser::where('blocked_by', $user_id)
                ->pluck('user_id');
        }

        $post = SocialConnectPost::with(['likes' => function ($q) use ($user_id) {
            $q->where('user_id', $user_id);
        }])
            ->with(['bookmarks' => function ($q) use ($user_id) {
                $q->where('user_id', $user_id);
            }])
            ->with('user')
            ->withExists(['reported' => function ($q) use ($user_id) {
                $q->where('reported_by', $user_id);
            }])
            ->when($user_id, function ($q) use ($blockedUserIds, $user_id) {
                $q->with([
                    'comments' => function ($q) use ($blockedUserIds, $user_id) {
                        $q->whereNotIn('user_id', $blockedUserIds)
                            ->withExists(['reportedComment' => function ($q2) use ($user_id) {
                                $q2->where('reported_by', $user_id);
                            }])
                            ->with('user');
                        $q->with([
                            'replies' => function ($q) use ($blockedUserIds, $user_id) {
                                $q->whereNotIn('user_id', $blockedUserIds)
                                    ->withExists(['reportedReply' => function ($q2) use ($user_id) {
                                        $q2->where('reported_by', $user_id);
                                    }])
                                    ->with('user');
                            }
                        ]);
                    }
                ]);
            })
            ->when($user_id, function ($q) use ($blockedUserIds) {
                $q->withCount([
                    'comments as comments_count' => function ($q) use ($blockedUserIds) {
                        $q->whereNotIn('user_id', $blockedUserIds);
                    },
                    'bookmarks',
                    'likes'
                ]);
            })
            ->findOrFail($post_id);

        $followingIdsArray = Auth::user() ? Auth::user()->followings()->pluck('user_id') : collect();

        return new SocialConnectPostResource($post, $followingIdsArray);
    }

    public function getFollowersCount(User $user): int
    {
        return UserFollower::where('user_id', $user->id)
            ->count();
    }

    public function getUserPostsCount(User $user): int
    {
        return SocialConnectPost::where('user_id', $user->id)
            ->count();
    }

    public function sendNewCommentNotification(SocialConnectPost $post, Comment $comment, User $user): void
    {
        if ($post->user_id !== $user->id) {
            $postOwner = $post->user;
            $commentedBy = $user;
            $commenterFullName = "{$commentedBy->first_name} {$commentedBy->last_name}";

            $title = "{$commenterFullName} commented on your post";
            $body = Str::limit($comment->comment, 100);

            SendSingleUserNotificationJob::dispatch(
                $postOwner->id,
                $title,
                $body,
                [
                    'post_id' => (string)$post->id,
                    'comment_id' => (string)$comment->id,
                    'url' => "akinaconnect://social-connect-detail/{$post->id}",
                    'web_url' => "/social-connect/post/{$post->id}",
                    'type' => NotificationType::INDIVIDUAL->value,
                    'thumbnail' => $commentedBy->profile_photo,
                    'subject' => $commenterFullName,
                ]
            );
        }
    }
}
