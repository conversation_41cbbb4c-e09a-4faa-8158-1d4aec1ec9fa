<?php

namespace App\Services;

use App\Mail\SendOTP;
use App\Models\OTP;
use App\Models\User;
use Illuminate\Support\Facades\Mail;

class OTPService
{
    public function createOTP(User $user): int
    {
        $otpCode = rand(1000, 9999);

        $otp = new OTP();
        $otp->otp = $otpCode;
        $otp->user_id = $user->id;

        $otp->save();

        return $otpCode;
    }

    public function createAndSendOTP(User $user): int
    {
        $user->otps()->update(['is_used' => true]);

        $otp = $this->createOTP($user);

        Mail::to($user)->send(new SendOTP($user, $otp));

        return $otp;
    }

    public function getOTP(User $user)
    {
        return $user->otps->where('is_used', false)
            ->sortByDesc('created_at')
            ->first()?->otp;
    }

    public function verifyOTP(User $user, $otp): bool
    {
        $otp = $user->otps->where('is_used', false)
            ->where('otp', $otp)
            ->first();

        if ($otp) {
            $otp->is_used = true;
            $otp->save();
        }

        return !!$otp;
    }
}
