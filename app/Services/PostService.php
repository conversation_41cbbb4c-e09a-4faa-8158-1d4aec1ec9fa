<?php

namespace App\Services;

use App\Enums\NotificationType;
use App\Enums\PostStatus;
use App\Http\Resources\PostResource;
use App\Jobs\SendNotificationToTopicJob;
use App\Models\BlockedUser;
use App\Models\Category;
use App\Models\Module;
use App\Models\Post;
use App\Models\Scopes\PostPublishedScope;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;

class PostService
{
    public function getDetail($post_id): PostResource
    {
        $user_id = Auth::user()->id ?? 0;


        $blockedUserIds = collect();
        if ($user_id > 0) {
            $blockedUserIds = BlockedUser::where('blocked_by', $user_id)
                ->pluck('user_id');
        }

        $post = Post::with(['likes' => function ($q) use ($user_id) {
            $q->where('user_id', $user_id);
        }])
            ->with('categories')
            ->with(['bookmarks' => function ($q) use ($user_id) {
                $q->where('user_id', $user_id);
            }])
            ->when($user_id, function ($q) use ($blockedUserIds, $user_id) {
                $q->with([
                    'comments' => function ($q) use ($blockedUserIds, $user_id) {
                        $q->whereNotIn('user_id', $blockedUserIds)
                            ->withExists(['reportedComment' => function ($q2) use ($user_id) {
                                $q2->where('reported_by', $user_id);
                            }])
                            ->with('user');
                        $q->with([
                            'replies' => function ($q) use ($blockedUserIds, $user_id) {
                                $q->whereNotIn('user_id', $blockedUserIds)
                                    ->withExists(['reportedReply' => function ($q2) use ($user_id) {
                                        $q2->where('reported_by', $user_id);
                                    }])
                                    ->with('user');
                            }
                        ]);
                    }
                ]);
            })
            ->findOrFail($post_id);

        return new PostResource($post);
    }


    /**
     * @throws ValidationException
     * @throws \Exception
     */
    public function createPost(array $data, string $userType = 'user'): array
    {
        $status = $userType === 'admin' ? PostStatus::Published : PostStatus::Pending;
        $message = $userType === 'admin' ? 'Post added successfully' : 'Add post request submitted successfully';

        $categoryIds = $this->validateCategories($data['category_ids'] ?? [], $data['module_id']);

        $data['thumbnail'] = $this->uploadThumbnail($data['thumbnail'] ?? null, $data['title']);
        $data['status'] = $status;

        $post = Post::create($data);

        if (!empty($categoryIds)) {
            $post->categories()->attach($categoryIds);
        }

        if ($userType == 'admin') {
            $this->sendNewPostNotification($post);
        }

        return [
            'message' => $message,
            'post' => $post,
        ];
    }

    /**
     * @throws ValidationException
     */
    public function updatePost(int $postId, array $data, string $userType = 'user'): array
    {
        $categoryIds = $this->validateCategories($data['category_ids'] ?? [], $data['module_id']);

        $post = Post::withoutGlobalScope(PostPublishedScope::class)->findOrFail($postId);

        $message = $userType === 'admin' ? 'Post updated successfully' : 'Post update requested successfully';
        if ($userType !== 'admin') {
            $post->status = PostStatus::Pending;
        }

        $post->title = $data['title'];
        $post->content = $data['content'];

        if (!empty($data['thumbnail'])) {
            $post->thumbnail = $this->uploadThumbnail($data['thumbnail'], $data['title']);
        }

        $post->save();

        if (!empty($categoryIds)) {
            $post->categories()->sync($categoryIds);
        }

        return [
            'message' => $message,
            'post' => $post,
        ];
    }

    /**
     * @throws ValidationException
     */
    private function validateCategories(array $categoryIds, int $moduleId): array
    {
        if (empty($categoryIds)) {
            return [];
        }

        // Validate categories belong to the correct module
        $validCategories = Category::whereIn('id', $categoryIds)
            ->where('module_id', $moduleId)
            ->pluck('id')
            ->toArray();

        foreach ($categoryIds as $categoryId) {
            if (!in_array($categoryId, $validCategories)) {
                throw ValidationException::withMessages([
                    'category_ids' => ["Category ID $categoryId is not associated with module ID $moduleId."]
                ]);
            }
        }

        return $validCategories;
    }

    private function uploadThumbnail($thumbnail, string $title): ?string
    {
        if (!$thumbnail) {
            return null;
        }

        $extension = $thumbnail->getClientOriginalExtension();
        $imageName = time() . '_' . Str::slug($title) . '-thumbnail.' . $extension;

        $path = $thumbnail->storeAs('post_photos', $imageName, 's3');

        return config('filesystems.disks.s3.url') . $path;
    }


    public function sendNewPostNotification(Post $post): void
    {
        $module = Module::where('id', $post->module_id)->first();
        $title = "A new blog was added by Akina One";
        $body = "Check it out here: {$post->title}";
        $data = [
            'post_id' => (string)$post->id,
            'url' => "akinaconnect://blog-detail//{$post->id}/{$post->module_id}/{$module->title}",
            'web_url' => "/{$module->slug}/post/{$post->id}",
            'thumbnail' => $post->thumbnail,
            'type' => NotificationType::SYSTEM_BROADCAST->value,
            'subject' => $title,
        ];

        $topic = config('firebase.default_topic');

        SendNotificationToTopicJob::dispatch($topic, $title, $body, $data);
    }
}
