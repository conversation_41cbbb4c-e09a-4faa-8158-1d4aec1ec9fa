<?php

namespace App\Services;


use App\Enums\UserMembershipType;
use App\Enums\UserType;
use App\Models\StripePaymentLog;
use App\Models\StripeWebhookLog;
use App\Models\User;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;
use Stripe\Exception\ApiErrorException;
use Stripe\StripeClient;

class StripeWebhookLoggingService
{
    protected MembershipService $membershipService;

    /**
     * StripeWebhookLoggingService constructor.
     */
    public function __construct(MembershipService $membershipService)
    {
        $this->membershipService = $membershipService;
    }

    public function logEvent(array $event): StripeWebhookLog
    {
        return StripeWebhookLog::create([
            'event_id' => $event['id'],
            'type' => $event['type'],
            'customer_id' => $event['data']['object']['customer'] ?? null,
            'payload' => $event,
            'status' => 'received',
        ]);
    }

    public function markAsProcessed(string $eventId, string $notes = null): void
    {
        StripeWebhookLog::where('event_id', $eventId)
            ->update([
                'status' => 'processed',
                'processing_notes' => $notes
            ]);
    }

    public function markAsFailed(string $eventId, string $error): void
    {
        StripeWebhookLog::where('event_id', $eventId)
            ->update([
                'status' => 'failed',
                'processing_notes' => $error,
                'error' => $error
            ]);
    }

    public function handleUserTypeUpdate(string $eventType, array $event): void
    {
        $customerId = $event['data']['object']['customer'] ?? null;
        if (!$customerId) {
            $this->markAsFailed($event['id'] ?? 0, 'Customer ID not found in event data');
            return;
        }

        $user = User::where('stripe_id', $customerId)->first();
        if (!$user) {
            $this->markAsFailed($event['id'] ?? 0, 'User not found for customer ID: ' . $customerId);
            return;
        }

        switch ($eventType) {
            case 'invoice.payment_succeeded':
                $this->membershipService->makeUserMember($user, UserMembershipType::STRIPE_SUBSCRIPTION->value);
                break;

            case 'payment_intent.succeeded':
                $paymentObj = $event['data']['object'];
                if (!empty($paymentObj['metadata']['type']) && $paymentObj['metadata']['type'] === UserMembershipType::STRIPE_ONE_TIME->value) {
                    $this->membershipService->makeUserMember($user, UserMembershipType::STRIPE_ONE_TIME->value);
                    $currency = strtoupper($invoice['currency'] ?? 'USD');
                    $amount = $paymentObj['amount_received'] / 100;
                    $this->stripePaymentLog($user, $event, $amount, $currency);

                    if (!empty($paymentObj['metadata']['price_id'])) {
                        $isPlanAdded = $this->membershipService->addUserPurchasedPlan($user, $paymentObj['metadata']['price_id']);

                        if (!$isPlanAdded) {
                            $this->markAsFailed($event['id'] ?? 0, 'Failed to add purchased plan for user: ' . $user->id);
                        }
                    }
                }
                break;

            case 'customer.subscription.deleted':
            case 'customer.subscription.updated':
                $status = $event['data']['object']['status'];
                if (in_array($status, ['canceled', 'unpaid', 'incomplete_expired'])) {
                    $activeSubscriptions = $user->subscriptions
                        ->first(fn($subscription) => $subscription->active());
                    if ($activeSubscriptions) {
                        Log::debug('Another subscription is active, no need to make user free. sub_id' . $event['data']['object']['id']);
                    } else {
                        $this->membershipService->makeUserFree($user);
                    }
                }
                break;
        }
    }

    /**
     * @throws ApiErrorException
     */
    public function handleRenewalTracking(array $event): void
    {
        $invoice = $event['data']['object'];
        $customerId = $invoice['customer'] ?? null;

        if (!$customerId) {
            $this->markAsFailed($event['id'] ?? 0, 'Customer ID not found in event data');
            return;
        }

        $user = User::where('stripe_id', $customerId)->first();
        if (!$user) {
            $this->markAsFailed($event['id'] ?? 0, 'User not found for customer ID: ' . $customerId);
            return;
        }

        // 🔁 Get renewal timestamp from invoice
        $subscriptionId = $invoice['subscription'] ?? null;
        $amount = $invoice['amount_paid'] / 100; // Stripe uses cents
        $currency = strtoupper($invoice['currency'] ?? 'USD');

        // 💾 Update subscription's next_renewal_at
        if ($subscriptionId) {
            $stripeSub = $this->membershipService->getStripe()->subscriptions->retrieve($subscriptionId);

            $user->subscriptions()
                ->where('stripe_id', $subscriptionId)
                ->update([
                    'next_renewal_at' => Carbon::createFromTimestamp($stripeSub->current_period_end),
                ]);
        }

        $this->stripePaymentLog($user, $event, $amount, $currency);
    }

    public function stripePaymentLog(User $user, array $event, int $amount, string $currency = 'USD'): void
    {
        $invoice = $event['data']['object'];
        $paymentIntentId = $invoice['id'] ?? null;

        StripePaymentLog::create([
            'user_id' => $user->id,
            'action_type' => $event['type'],
            'amount' => $amount,
            'currency' => $currency,

            'customer_id' => $event['data']['object']['customer'] ?? null,
            'subscription_id' => $invoice['subscription'] ?? null,
            'payment_intent_id' => $event['type'] == 'payment_intent.succeeded' ? $paymentIntentId : null,

            'status' => 'succeeded',
            'metadata' => [
                'invoice_id' => $invoice['id'],
                'payment_intent' => $paymentIntentId,
            ],
            'payload' => $event,
        ]);
    }
}
