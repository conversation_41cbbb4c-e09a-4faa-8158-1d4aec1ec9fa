<?php

namespace App\Services;

use App\Models\Organization;

class OrganizationService
{
    public function makeStudentViaOrg($organizationId): bool
    {
        if (!$organizationId) {
            return false;
        }

        $organization = Organization::find($organizationId);
        if (!$organization) {
            return false;
        }

        if ($organization->is_student_org) {
            return true;
        }

        return false;
    }
}
