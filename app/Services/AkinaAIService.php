<?php

namespace App\Services;

use App\Models\AkinaAIConversation;
use App\Models\AkinaAIMessage;
use Illuminate\Support\Carbon;
use OpenAI;

class AkinaAIService
{
    protected $openAIClient;

    public function __construct()
    {

    }

    public function streamChatWithOpenAI($messages, $onData): void
    {
        $openAIClient = OpenAI::client(config('openai.api_key'));

        $stream = $openAIClient->chat()->createStreamed([
            'model' => 'gpt-3.5-turbo', // or gpt-3.5-turbo
            /*'messages' => [
                ['role' => 'system', 'content' => 'You are a helpful assistant.'],
                ['role' => 'user', 'content' => $message],
            ],*/
            'messages' => $messages,
            'stream' => true,
        ]);

        foreach ($stream as $chunk) {
            if (isset($chunk['choices'][0]['delta']['content'])) {
                $onData($chunk['choices'][0]['delta']['content']);
            }
        }
    }

    public function getConversationIdFromExternalId(array $data): int
    {
        $akinaConversation = AkinaAIConversation::where('external_conversation_id', $data['conversation_id'])
            ->select('id')
            ->first();

        if ($akinaConversation) {
            return $akinaConversation->id;
        } else {
            $conversation = $this->createConversation($data);
            return $conversation->id;
        }
    }

    private function getFirstWords(string $text, int $wordLimit = 200): string
    {
        $words = preg_split('/\s+/', trim($text));
        $trimmed = count($words) > $wordLimit;
        $firstWords = array_slice($words, 0, $wordLimit);

        return implode(' ', $firstWords) . ($trimmed ? '...' : '');
    }

    public function createConversation(array $data): AkinaAIConversation
    {
        return AkinaAIConversation::create([
            'user_id' => $data['user_id'],
            'title' => $this->getFirstWords($data['user_query'], 20),
            'external_conversation_id' => $data['conversation_id'],
        ]);
    }

    public function createMessage(array $data, int $conversation_id): bool
    {
        $now = Carbon::now();

        $defaultMessage =
            [
                'conversation_id' => $conversation_id,
                'user_id' => $data['user_id'],
                'created_at' => $now,
                'updated_at' => $now,
            ];

        return AkinaAIMessage::insert([
            array_merge($defaultMessage, [
                'sender_type' => 'user',
                'external_message_id' => $data['question_id'],
                'content' => $data['user_query'],
            ]),
            array_merge($defaultMessage, [
                'sender_type' => 'ai',
                'external_message_id' => $data['answer_id'],
                'content' => $data['llm_response'],
            ]),
        ]);
    }

}
