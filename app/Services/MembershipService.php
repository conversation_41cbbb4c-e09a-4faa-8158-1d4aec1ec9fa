<?php

namespace App\Services;

use App\Enums\UserMembershipType;
use App\Enums\UserType;
use App\Models\Plan;
use App\Models\SubscriptionCancellationRequest;
use App\Models\User;
use App\Models\UserPlan;
use App\Models\UserStatusHistory;
use Carbon\Carbon;
use Laravel\Cashier\Cashier;
use Laravel\Cashier\Exceptions\IncompletePayment;
use Mockery\Exception;
use Stripe\Exception\ApiErrorException;
use Stripe\PaymentIntent;
use Stripe\Price;
use Stripe\Stripe;
use Laravel\Cashier\Subscription;
use Stripe\StripeClient;

class MembershipService
{
    protected StripeClient $stripe;

    public function __construct(private PushNotificationService $pushNotificationService)
    {
        Stripe::setApiKey(config('cashier.secret'));
        $this->stripe = Cashier::stripe();
    }

    public function getStripe()
    {
        return $this->stripe;
    }

    public function makeUserMember(User $user, string $userMembershipType): bool
    {

        $user->user_type = UserType::PREMIUM->value;
        $user->user_membership_type = $userMembershipType;

        $isUserSaved = $user->save();

        $this->maintainStatusHistory($user, $userMembershipType);
        $this->pushNotificationService->unSubscribeFromTopic($user, config('firebase.free_user_topic'));
        $this->pushNotificationService->subscribeToTopic($user, config('firebase.premium_user_topic'));

        return $isUserSaved;
    }

    public function makeUserFree(User $user): bool
    {
        $user->user_type = UserType::FREE->value;
        $user->user_membership_type = null;
        $isUserSaved = $user->save();

        $this->maintainStatusHistory($user);
        $this->pushNotificationService->unSubscribeFromTopic($user, config('firebase.premium_user_topic'));
        $this->pushNotificationService->subscribeToTopic($user, config('firebase.free_user_topic'));

        return $isUserSaved;
    }

    private function maintainStatusHistory(User $user, string|null $userMembershipType = null): bool
    {
        $userStatusHistory = new UserStatusHistory();
        $userStatusHistory->user_id = $user->id;
        $userStatusHistory->user_type = $user->user_type;
        $userStatusHistory->user_membership_type = $userMembershipType ?? $user->user_membership_type;

        return $userStatusHistory->save();
    }

    /**
     * @throws Exception
     * @throws IncompletePayment
     */
    public function createSubscription(User $user, string $priceId, string $paymentMethod)
    {

        return $user->newSubscription('default', $priceId)
            ->create($paymentMethod);
    }

    /**
     * @throws ApiErrorException
     */
    public function makeOneTimePayment(User $user, string $priceId, string $paymentMethod)
    {
        $amountInCents = $this->getAmountFromPrice($priceId);

        $intent = PaymentIntent::create([
            'amount' => $amountInCents,
            'payment_method' => $paymentMethod,
            'currency' => 'usd',
            'customer' => $user->stripe_id,
            'automatic_payment_methods' => [
                'enabled' => true,
                'allow_redirects' => 'never',
            ],
            'metadata' => [
                'type' => UserMembershipType::STRIPE_ONE_TIME->value,
                'price_id' => $priceId,
                'user_id' => $user->id,
            ],
        ]);

        $intent->confirm();

        return $intent;
    }

    public function addUserPurchasedPlan(User $user, string $priceId): bool
    {
        $plan = Plan::where('stripe_price_id', $priceId)->first();

        if ($plan) {
            $userPlan = new UserPlan();
            $userPlan->user_id = $user->id;
            $userPlan->plan_id = $plan->id;
            $userPlan->start_date = Carbon::now();
            $userPlan->end_date = Carbon::now()->addYear();
            return $userPlan->save();
        }

        return false;
    }

    /**
     * @throws ApiErrorException
     */
    public function getAmountFromPrice(string $priceId): int
    {
        $price = Price::retrieve($priceId);

        return $price->unit_amount;
    }

    /**
     * @throws ApiErrorException
     */
    public function isPlanRecurring(string $priceId): bool
    {
        $price = Price::retrieve($priceId);

        return (bool)$price?->recurring?->interval;
    }

    public function subscriptionCancellationRequest(Subscription $subscription, string $reason = 'User requested cancellation'): bool
    {
        $cancellationRequest = new SubscriptionCancellationRequest();
        $cancellationRequest->user_id = $subscription->user_id;
        $cancellationRequest->subscription_id = $subscription->stripe_id;
        $cancellationRequest->reason = $reason;

        return $cancellationRequest->save();
    }
}
