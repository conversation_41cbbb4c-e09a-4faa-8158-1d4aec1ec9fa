<?php

namespace App\Services;

use App\Http\Resources\PodCastResource;
use App\Models\BlockedUser;
use App\Models\PodCast;
use Illuminate\Support\Facades\Auth;

class PodCastService
{
    public function getDetail(string $podcast_id, string $module_id): PodCastResource
    {
        $user_id = Auth::user()->id ?? 0;

        $blockedUserIds = collect();
        if ($user_id > 0) {
            $blockedUserIds = BlockedUser::where('blocked_by', $user_id)
                ->pluck('user_id');
        }

        $podcast = PodCast::with(['likes' => function ($q) use ($user_id) {
            $q->where('user_id', $user_id);
        }])
            ->with(['bookmarks' => function ($q) use ($user_id) {
                $q->where('user_id', $user_id);
            }])
            ->with('category')
            ->when($user_id, function ($q) use ($blockedUserIds, $user_id) {
                $q->with([
                    'comments' => function ($q) use ($blockedUserIds, $user_id) {
                        $q->whereNotIn('user_id', $blockedUserIds)
                            ->withExists(['reportedComment' => function ($q2) use ($user_id) {
                                $q2->where('reported_by', $user_id);
                            }])
                            ->with('user');
                        $q->with([
                            'replies' => function ($q) use ($blockedUserIds, $user_id) {
                                $q->whereNotIn('user_id', $blockedUserIds)
                                    ->withExists(['reportedReply' => function ($q2) use ($user_id) {
                                        $q2->where('reported_by', $user_id);
                                    }])
                                    ->with('user');
                            }
                        ]);
                    }
                ]);
            })
            ->findOrFail($podcast_id);

        $podcast->audio_link = $this->getDestinationUrl($podcast->audio_link);
        $podcast['previous_record'] = PodCast::where('id', '<', $podcast->id)->where('category_id', $podcast->category_id)->orderBy('id', 'desc')->select('id')->first();
        $podcast['next_record'] = PodCast::where('id', '>', $podcast->id)->where('category_id', $podcast->category_id)->orderBy('id')->select('id')->first();
        $podcast['module_id'] = $module_id;


        return new PodCastResource($podcast);
    }

    public function getDestinationUrl($url): string
    {
        $final_url = $url;

        $explodeUrl = explode("https%3A", $url);
        if (count($explodeUrl) > 1) {
            $final_url = urldecode("https%3A" . $explodeUrl[1]);
        }

        return $final_url;
    }

    public function getFirstWordsFromHTML(string $html, int $wordLimit = 50): string {
        // Strip HTML tags and decode HTML entities
        $plainText = html_entity_decode(strip_tags($html), ENT_QUOTES, 'UTF-8');

        // Trim whitespace and handle empty string
        $plainText = trim($plainText);
        if (empty($plainText)) {
            return '';
        }

        // Normalize multiple spaces and newlines
        $plainText = preg_replace('/\s+/', ' ', $plainText);

        // Split into words
        $words = preg_split('/\s+/', $plainText);

        // Handle case where text has fewer words than limit
        if (count($words) <= $wordLimit) {
            return $plainText;
        }

        // Slice first N words and join them
        return implode(' ', array_slice($words, 0, $wordLimit));
    }

}
