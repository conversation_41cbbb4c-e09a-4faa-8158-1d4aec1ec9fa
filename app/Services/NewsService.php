<?php

namespace App\Services;

use App\Http\Resources\NewsResource;
use App\Models\BlockedUser;
use App\Models\News;
use Illuminate\Support\Facades\Auth;

class NewsService
{
    public function getDetail($news_id): NewsResource
    {
        $user_id = Auth::user()->id ?? 0;

        $blockedUserIds = collect();
        if ($user_id > 0) {
            $blockedUserIds = BlockedUser::where('blocked_by', $user_id)
                ->pluck('user_id');
        }

        $news = News::with(['likes' => function ($q) use ($user_id) {
            $q->where('user_id', $user_id);
        }])->with(['bookmarks' => function ($q) use ($user_id) {
            $q->where('user_id', $user_id);
        }])
            ->when($user_id, function ($q) use ($blockedUserIds, $user_id) {
                $q->with([
                    'comments' => function ($q) use ($blockedUserIds, $user_id) {
                        $q->whereNotIn('user_id', $blockedUserIds)
                            ->withExists(['reportedComment' => function ($q2) use ($user_id) {
                                $q2->where('reported_by', $user_id);
                            }])
                            ->with('user');
                        $q->with([
                            'replies' => function ($q) use ($blockedUserIds, $user_id) {
                                $q->whereNotIn('user_id', $blockedUserIds)
                                    ->withExists(['reportedReply' => function ($q2) use ($user_id) {
                                        $q2->where('reported_by', $user_id);
                                    }])
                                    ->with('user');
                            }
                        ]);
                    }
                ]);
            })
            ->findOrFail($news_id);

        return new NewsResource($news);
    }
}
